- 中文单词制卡：https://theajack.github.io/cnchar/
- ofd转pdf
- pdf盖骑缝章
- PDF挖空支持点击朗读，类似点读机
- flomo制卡： https://github.com/springrain1/flomo2Anki
- 思源笔记闪卡导入
- 模板开发：一首小古文，点开某一句， 就翻转出对应译文，再点一下 ，又变回原古文
- 文本制卡支持指定标签
- PDF制卡支持串联其他卡片，类似思维导图制卡
- 添加图床功能，参考：https://github.com/PicGo/flutter-picgo/blob/dev/lib/api/aliyun_api.dart
- xmind制卡只有空图片时不显示
- PDF批注转导图可编辑，然后用导图制卡
- PDF问答批注支持公共题干类型
- 文本清洗功能：去除多余换行、题号前换行、选项前换行、书签缩进
- 类Anki闪卡软件开发：参考无痛单词app，无需手动选择掌握程度
- 图片/PDF智能挖空: 基于大模型+OCR定位

v2.2.0
1. 新增单词制卡，大幅提升解析速度，单词模板迎来大更新
2. 新增双层PDF功能，支持将扫描版PDF转成可搜索可复制PDF

v2.1.0
1. 图片制卡支持自动OCR识别文字；支持自定义设置遮罩颜色；Mac端支持使用内置截图快捷键；支持图片裁剪/旋转；支持图片批注(箭头、矩形、自由文字等)；支持缩放和平移图片，以方便添加和修改遮罩
2. 新增图片OCR功能，支持离线使用，支持移动端使用，支持批量图片OCR
3. 新增卡片OCR功能，支持对已有卡片中的图片进行OCR识别并将识别结果填充到指定字段中
4. 新增PDF OCR功能，支持批量识别PDF页面提取文本
5. Word制卡支持解析数学公式
6. 视频笔记支持在线视频(B站、Youtube等)，需安装浏览器插件guru connector
7. 图片挖空、文本挖空、导图制卡等众多模板全新改版
8.  微信读书制卡模板更新，同时支持回链
9.  新增国际化支持，支持简体中文、英文、繁体中文、日文界面
10. PDF组合支持设置页面布局顺序
11. PDF去水印新增多种去除方式
12. Anki同步支持多用户，支持设置最大同步负载
13. 修复微信读书制卡自己导入的书籍不显示问题
14. 修复卡片朗读挖空内容不发音问题
15. 修复下拉列表添加新元素不立即生效问题
16. 修复AI制卡提示词编辑页面暗色模式下看不清问题
17. 修复主题设置"跟随系统"失效问题
18. 修复win上关闭软件后台程序未退出问题
19. 修复win上PDF回链点击不生效问题
20. 修复Ankidroid平板端图片挖空遮罩不显示问题
21. mac安装包添加签名证书，避免系统提示"无法打开xx"的报错
22. 其他优化和细节完善


v2.0.0
1. 全新UI界面，全新内核，支持电脑端、移动端多平台使用，性能和稳定性提升
2. 制卡支持导出apkg、直连ankiconnect、直连AnkiDroid等多种导出模式
3. 视频笔记支持截图编辑，支持截图直接挖空制卡
4. 导图制卡更新：
   1） 大纲模式下同样支持递归查看子节点
   2） Xmind制卡图片提取优化，避免有时图片不显示问题，支持保留文本样式，支持对文本样式设置挖空
   3） 新增支持知悉思维导图制卡
5. 新增图片制卡，支持截图快速挖空制卡
6. word制卡支持保留文本颜色，支持对自定义文本颜色、文本高亮颜色挖空
7. 文本制卡更新；
   1) 挖空题支持同时指定多种挖空语法
   2) Markdown制卡支持指定图片文件目录
   3) 问答题支持匹配任意字段数量模板制卡
8. Excel制卡支持指定标签列
9. 微信读书制卡支持根据目录自动创建子牌组，支持指定时间范围内的笔记制卡
10. Anki同步支持移动端之间搭建服务器实现局域网同步，无需电脑
11. 其他优化和细节完善
