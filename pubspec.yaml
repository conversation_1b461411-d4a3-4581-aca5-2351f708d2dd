name: anki_guru
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 2.1.0+136

environment:
  sdk: ^3.5.3

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  intl: ^0.20.2
  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  app_links: ^6.3.3
  # cupertino_icons: ^1.0.8
  dio: ^5.7.0
  file_picker: 10.2.1
  # filesystem_picker: ^4.1.0
  # flutter_client_sse: ^2.0.3
  # flutter_speed_dial: ^7.0.0
  gap: ^3.0.1
  get_storage: ^2.1.1
  get: ^4.7.2
  http: ^1.2.2
  logger: ^2.6.1
  media_kit_libs_video: ^1.0.5 # Native video dependencies.
  media_kit_video: ^1.2.5 # For video rendering.
  media_kit: ^1.1.11 # Primary package.
  open_file: ^3.5.9
  path_provider: ^2.1.5
  # percent_indicator: ^4.2.3
  permission_handler: ^11.3.1
  pro_image_editor: ^10.5.4
  # serious_python: ^0.8.2
  share_handler: ^0.0.22
  share_plus: ^10.1.3
  shared_preferences: ^2.3.3
  skeletonizer: ^2.1.0
  super_clipboard: ^0.9.1
  syncfusion_flutter_pdf: ^30.1.42
  # syncfusion_flutter_pdfviewer: ^30.1.42
  toastification: ^3.0.3
  flex_color_scheme: ^8.0.2
  google_fonts: ^6.2.1
  webview_flutter: ^4.10.0
  flutter_inappwebview: ^6.1.5
  # searchfield: ^1.2.2
  image: ^4.3.0
  path: ^1.9.0
  subtitle: ^0.1.0-beta.3
  flutter_slidable: ^3.1.2
  url_launcher: ^6.3.1
  font_awesome_flutter: ^10.8.0
  qr_flutter: ^4.1.0
  crypto: ^3.0.6
  flutter_udid: ^4.0.0
  convert: ^3.1.2
  package_info_plus: ^8.1.1
  pick_or_save:
    git:
      url: https://github.com/chaudharydeepanshu/pick_or_save.git
  docx_to_text: ^1.0.1
  rinf: ^8.7.1

  ulid: ^2.0.1
  file_selector: ^1.0.3
  hive:
    git:
      url: https://github.com/isar/hive
  isar_flutter_libs: ^4.0.0-dev.14
  external_path:
    git:
      url: https://github.com/mustafaG9/external_path.git
      ref: main
  open_file_manager: ^2.0.1
  hotkey_manager: ^0.2.3
  pasteboard: ^0.3.0
  desktop_drop: ^0.6.1
  # shadcn_ui: ^0.28.0
  shadcn_ui: ^0.26.5
    # git:
    #   url: https://github.com/nank1ro/flutter-shadcn-ui.git
  # flutter_side_menu: ^0.5.41
  # fluent_ui: ^4.10.0
  flutter_localization: ^0.3.0
  launch_at_startup: ^0.5.1
  # tray_manager: ^0.3.1
  # window_manager_plus: ^1.0.5
  flutter_js: ^0.8.5
  audioplayers: ^6.1.0
  web_socket_channel: ^3.0.1
  excel: ^4.0.6
  csv: ^6.0.0
  markdown: ^7.3.0
  html: ^0.15.5
  shelf: ^1.4.2
  local_notifier: ^0.1.6
  flex_color_picker: ^3.7.0
  win32_registry: ^2.0.0
  window_size:
    git:
      url: https://github.com/google/flutter-desktop-embedding.git
      path: plugins/window_size
  shelf_web_socket: ^3.0.0
  shelf_router: ^1.1.4
  charset: ^2.0.1
  flutter_client_sse: ^2.0.3
  volume_controller: ^3.3.3
  screen_brightness: ^2.1.2
  shelf_cors_headers: ^0.1.5
  # pdfx: ^2.8.0
  # pdfrx: ^1.1.12
  in_app_purchase: ^3.1.11
  in_app_purchase_storekit: ^0.3.6+1
  htmltopdfwidgets: ^1.0.9
  pdf: ^3.11.3
  parchment: ^1.22.0
  flutter_quill: ^11.4.1
  markdown_quill: ^4.3.0
  dart_quill_delta: ^10.8.3
  device_info_plus: ^11.0.0
  # screen_capturer: ^0.2.3
  tuple: ^2.0.2
  flutter_machineid:
    path: third_party/flutter_machineid
  flutter_window_close: ^1.3.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^4.0.0
  integration_test:
    sdk: flutter
  msix: ^3.16.8
  build_runner: ^2.4.15

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  assets:
    # - app/app.zip
    - assets/images/
    - assets/i18n/
    - assets/models/
    - assets/fonts/
    - assets/anki/__anki-persistence.js
    - assets/markmap.index.mjs
    - assets/mammoth.browser.min.js
    - assets/parse_html.js
    - assets/jsonrepair.min.js

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: SourceHanSansSC
      fonts:
        - asset: assets/fonts/SourceHanSansSC-Normal.ttf
          weight: 400
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

msix_config:
  display_name: PDF Guru Anki
  msix_version: *******
  protocol_activation: https, guru2 # Add the protocols to activate the app
  app_uri_handler_hosts: guru.kevin2li.top # Add the app uri handler hosts. You can't use patterns here.
  capabilities: internetClient
  debug: false
