import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/controllers/anki/tts/data.dart';
import 'package:ulid/ulid.dart';

class CardTtsParams extends GetxController {
  // 表单参数
  final cardIds = ''.obs;
  final rate = 0.0.obs;
  final volume = 0.0.obs;
  final pitch = 0.0.obs;
  final lang = 'zh-CN'.obs;
  final voice = 'zh-CN-XiaoxiaoNeural'.obs;
  final text = ''.obs;
}

class TextTtsParams extends GetxController {
  final sepList = [
    {"label": "---", "value": "---"},
  ];
  // 表单参数
  final text = ''.obs;
  final sep = "---".obs;
  final isUseSep = false.obs;
  final outputDir = ''.obs;

  @override
  void onInit() async {
    super.onInit();
    outputDir.value = await PathUtils.downloadDir;
    logger.i(outputDir.value);
  }
}

class CardTTSPageController extends GetxController {
  // 已有数据
  final roiModelList = <String>[].obs;
  final fieldList = <String>[].obs;
  final cardModel = "".obs;
  final tabController = ShadTabsController(value: 'card_tts');
  final langOptions = lang_options;
  final voiceOptions = voice_options;
  final roiVoiceOptions = <Map<String, String>>[].obs;
  final noteList = <dynamic>[].obs;
  final modelFieldConfigs = <String, Map<String, Map<String, dynamic>>>{}.obs;
  // 表单参数
  final rate = 0.0.obs;
  final volume = 100.0.obs;
  final pitch = 0.0.obs;
  final lang = 'zh-CN'.obs;
  final voice = 'zh-CN-XiaoxiaoNeural'.obs;
  final cardTtsParams = Get.put(CardTtsParams());
  final textTtsParams = Get.put(TextTtsParams());
  // 控制器
  final progressController = Get.find<ProgressController>();
  final messageController = Get.find<MessageController>();
  final settingController = Get.find<SettingController>();
  final ankiConnectController = Get.find<AnkiConnectController>();
  @override
  void onInit() async {
    roiVoiceOptions.value = voiceOptions['zh-CN'] ?? [];
    if (settingController.cardMode.value == "ankiconnect") {
      roiModelList.value = [];

      // Check if AnkiConnectController data is loaded, if not, load it
      if (ankiConnectController.modelList.isEmpty) {
        await ankiConnectController.resetAnkiConnectData();
      }

      if (ankiConnectController.modelList.isNotEmpty) {
        cardModel.value = ankiConnectController.modelList[0];
        updateFieldList(cardModel.value);
      }
    }
    super.onInit();
  }

  @override
  void onClose() {
    cardTtsParams.dispose();
    textTtsParams.dispose();
    super.onClose();
  }

  Future<void> updateFieldList(String modelName) async {
    if (settingController.cardMode.value == "ankiconnect") {
      try {
        fieldList.value =
            await AnkiConnectController().updateFieldList(modelName);
      } catch (e) {
        logger.e("updateFieldList error: $e");
      }
    }
  }

  Future<Map<String, dynamic>> ttsRequest(
      String text, String outputPath) async {
    final data = {
      "lang": lang.value,
      "voice": voice.value,
      "rate": "${rate.value >= 0 ? '+' : ''}${rate.value.toInt()}%",
      "pitch": "${pitch.value >= 0 ? '+' : ''}${pitch.value.toInt()}Hz",
      "volume": "${volume.value >= 0 ? '+' : ''}${volume.value.toInt()}%",
      "output_format": "audio-24khz-96kbitrate-mono-mp3",
      "timeout": 20,
      "text": text,
      "output_path": outputPath,
      "sub_path": "",
      "show_progress": false,
    };
    logger.d("data: $data");
    try {
      // 发送请求
      final resp = await messageController.request(data, "tts");
      logger.d("resp: $resp");
      return {
        "status": resp.status,
        "data": resp.data,
        "message": resp.message,
      };
    } catch (e) {
      logger.e("ttsRequest error: $e");
      return {
        "status": "error",
        "message": e.toString(),
      };
    }
  }

  void submit(BuildContext context) async {
    if (tabController.selected == 'card_tts') {
      if (Platform.isAndroid || Platform.isIOS) {
        showToastNotification(
            context, "anki.tts.feature_not_supported_on_current_system".tr, "",
            type: "error");
        return;
      }
      if (noteList.isEmpty) {
        showToastNotification(
            context, "anki.tts.please_select_cards_first".tr, "",
            type: "error");
        return;
      }
      progressController.reset(
        showOutputHint: false,
        numberButtons: 0,
      );
      progressController.showProgressDialog(context);
      final tempDir = await PathUtils.tempDir;
      try {
        final resultNotes = <Map<String, dynamic>>[];
        for (var i = 0; i < noteList.length; i++) {
          final note = noteList[i];
          final progress = ((i + 1) / noteList.length) * 100;
          progressController.updateProgress(
            status: "running",
            message: "anki.tts.processing_note"
                .trParams({'noteId': note['noteId'].toString()}),
            current: progress,
            total: 100.0,
          );
          final modelName = note['modelName'];
          final noteId = note['noteId'];
          final fields = note['fields'] as Map<String, dynamic>;

          // 获取该模型的字段配置
          final fieldConfig = modelFieldConfigs[modelName] ?? {};
          Map<String, String> resultFields = {};

          // 遍历 fieldConfig 而不是所有字段
          for (final entry in fieldConfig.entries) {
            final config = entry.value;
            // 获取原始字段
            final originField = entry.key;
            final originFieldValue = fields[originField]?['value'] ?? '';
            // 获取目标字段
            final targetField = config['targetField'];
            logger.w(targetField);
            final targetFieldValue = fields[targetField]?['value'] ?? '';
            // 移除已有的语音标签
            final targetCleanedValue =
                targetFieldValue.replaceAll(RegExp(r'\[sound:.*?\]\s*'), '');
            final originalCleanedValue =
                originFieldValue.replaceAll(RegExp(r'\[sound:.*?\]\s*'), '');
            if (config['removeVoice'] == true) {
              resultFields[originField] = originalCleanedValue;
              continue;
            }
            if (config['addVoice'] == false) {
              continue;
            }
            // 移除HTML标签，转换为纯文本
            String plainText =
                originalCleanedValue.replaceAll(RegExp(r'<[^>]*>'), '');
            // 将[[c1::xx]]替换为xx
            plainText = plainText.replaceAllMapped(
                RegExp(r'\[\[c\d+::(.*?)\]\]'),
                (match) => match.group(1) ?? '');
            // 将{{c1:xx}}替换为xx
            plainText = plainText.replaceAllMapped(
                RegExp(r'\{\{c\d+:(.*?)\}\}'), (match) => match.group(1) ?? '');
            plainText = plainText.trim();
            logger.d("plainText: $plainText");
            if (plainText.isEmpty) {
              continue;
            }
            // 生成语音
            final outputPath =
                PathUtils.join([tempDir, "${Ulid().toString()}.mp3"]);
            final ttsResult = await ttsRequest(plainText, outputPath);
            logger.w(ttsResult);
            if (ttsResult['status'] == 'success') {
              // 构建带有语音标签的内容
              final audioPath = ttsResult['data'].toString();
              await AnkiConnectController().storeMediaFile(audioPath);
              final audioTag = '[sound:${PathUtils(audioPath).name}]';
              // 更新目标字段的值
              resultFields[targetField] = "$targetCleanedValue $audioTag";
            } else {
              progressController.updateProgress(
                status: "error",
                message: "anki.tts.voice_generation_failed"
                    .trParams({'message': ttsResult['message'].toString()}),
                current: 0.0,
                total: 100.0,
              );
              return;
            }
          }
          // 构建结果note
          resultNotes.add({
            'id': noteId,
            'fields': resultFields,
          });
          final resp = await AnkiConnectController().updateNote(resultNotes[i]);
          logger.i("resp: $resp");
        }
        // 更新进度
        progressController.updateProgress(
          status: "completed",
          message: 'common.completed'.tr,
          current: 100.0,
          total: 100.0,
        );
      } catch (e) {
        logger.e("处理笔记失败: $e");
        progressController.updateProgress(
          status: "error",
          message: e.toString(),
          current: 0.0,
          total: 100.0,
        );
      }
    } else {
      if (textTtsParams.text.value.trim().isEmpty) {
        showToastNotification(
            context, "anki.tts.failed".tr, "anki.tts.text_cannot_be_empty".tr,
            type: "error");
        return;
      }
      if (textTtsParams.outputDir.value.trim().isEmpty) {
        showToastNotification(context, "anki.tts.failed".tr,
            "anki.tts.output_directory_cannot_be_empty".tr,
            type: "error");
        return;
      }
      logger.i(textTtsParams.outputDir.value);
      progressController.reset(
        showOutputHint: true,
        numberButtons: 3,
      );
      progressController.showProgressDialog(context);
      try {
        List<String> textList = [textTtsParams.text.value];
        if (textTtsParams.isUseSep.value) {
          textList = textTtsParams.text.value.split(textTtsParams.sep.value);
        }
        bool allSuccess = true;
        List<String> audioPaths = [];
        for (var i = 0; i < textList.length; i++) {
          progressController.updateProgress(
            status: "running",
            message: "anki.tts.processing".tr,
            current: i.toDouble(),
            total: textList.length.toDouble(),
          );
          final outputPath = PathUtils.join([
            textTtsParams.outputDir.value,
            "${DateTime.now().millisecondsSinceEpoch}.mp3"
          ]);
          final resp = await ttsRequest(textList[i], outputPath);
          if (resp["status"] == "success") {
            audioPaths.add(resp["data"]);
          } else {
            allSuccess = false;
            progressController.updateProgress(
              status: "error",
              message: "${resp["message"]}",
              current: 100.0,
              total: 100.0,
            );
            return;
          }
        }
        if (allSuccess) {
          progressController.updateProgress(
            status: "completed",
            message: 'common.completed'.tr,
            current: 100.0,
            total: 100.0,
          );
          progressController.outputPath.value = audioPaths[0];
        }
      } catch (e) {
        progressController.updateProgress(
          status: "error",
          message: e.toString(),
          current: 100.0,
          total: 100.0,
        );
      }
    }
  }

  // 更新字段配置的方法，增加模型参数
  void updateFieldConfig(String modelName, String fieldName,
      {bool? addVoice, bool? removeVoice, String? targetField}) {
    // 如果模型不存在，初始化模型配置
    if (!modelFieldConfigs.containsKey(modelName)) {
      modelFieldConfigs[modelName] = {};
    }

    // 如果字段不存在，初始化字段配置
    if (!modelFieldConfigs[modelName]!.containsKey(fieldName)) {
      modelFieldConfigs[modelName]![fieldName] = {
        'addVoice': false,
        'removeVoice': false,
        'targetField': fieldName,
      };
    }

    final config = modelFieldConfigs[modelName]![fieldName]!;
    if (addVoice != null) config['addVoice'] = addVoice;
    if (removeVoice != null) config['removeVoice'] = removeVoice;
    if (targetField != null) config['targetField'] = targetField;
    modelFieldConfigs.refresh();
  }
}
