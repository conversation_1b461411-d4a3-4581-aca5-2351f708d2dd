import 'dart:io';

import 'package:flutter/material.dart' hide Element, Text;
import 'package:get/get.dart' hide Node;
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/controllers/common.dart';
import 'dart:convert';

import 'package:ulid/ulid.dart';

class ClozeParams extends GetxController {
  // 表单参数
  final mode = "free_guess".obs;
  final isPerClozePerCard = false.obs;
  final clozeGrammar = <String>["bold"].obs;
  final isIgnoreGroup = true.obs;
  final primaryMaskColor = "ffff5656".obs;
  final secondaryMaskColor = "ffffeba2".obs;

  final errorFieldMapping = <String, String?>{}.obs;

  String validateFilePath(String field, String value, List<String> files) {
    if (value.isEmpty) {
      if (files.isEmpty) {
        return 'anki.word_card.please_select_file'.tr;
      }
    }
    final path = PathUtils(value);
    if (!path.exists()) {
      return 'anki.word_card.file_path_not_exist'.tr;
    }
    if (!path.isFile) {
      return 'anki.word_card.please_select_file_not_directory'.tr;
    }
    return '';
  }
}

class QaParams extends GetxController {
  final mode = "same_file".obs;
  final isQuestionSingleLine = false.obs;
  final isAnswerCloze = false.obs;
  final clozeGrammar = <String>["bold"].obs;
  final isShowSource = true.obs;

  final errorFieldMapping = <String, String?>{}.obs;

  String validateFilePath(String field, String value, List<String> files) {
    if (value.isEmpty) {
      if (files.isEmpty) {
        return 'anki.word_card.please_select_file'.tr;
      }
    }
    final path = PathUtils(value);
    if (!path.exists()) {
      return 'anki.word_card.file_path_not_exist'.tr;
    }
    if (!path.isFile) {
      return 'anki.word_card.please_select_file_not_directory'.tr;
    }
    return '';
  }

  Future<bool> validateAll() async {
    final controller = Get.find<WordCardFormController>();
    if (mode.value == 'same_file' && controller.selectedFilePaths.isEmpty) {
      errorFieldMapping['input'] = 'anki.word_card.please_select_file'.tr;
      return false;
    }
    if (mode.value == 'cross_file') {
      if (controller.qFile.value.isEmpty) {
        errorFieldMapping['qFile'] =
            'anki.word_card.please_select_question_file'.tr;
        return false;
      }
      if (controller.aFile.value.isEmpty) {
        errorFieldMapping['aFile'] =
            'anki.word_card.please_select_answer_file'.tr;
        return false;
      }
    }
    return true;
  }
}

class ChoiceParams extends GetxController {
  final mode = "answer_in_question".obs;
  final errorFieldMapping = <String, String?>{}.obs;
  String validateFilePath(String field, String value, List<String> files) {
    if (value.isEmpty) {
      if (files.isEmpty) {
        return 'anki.word_card.please_select_file'.tr;
      }
    }
    final path = PathUtils(value);
    if (!path.exists()) {
      return 'anki.word_card.file_path_not_exist'.tr;
    }
    if (!path.isFile) {
      return 'anki.word_card.please_select_file_not_directory'.tr;
    }
    return '';
  }
}

class JudgeParams extends GetxController {
  final mode = "same_file".obs;
  final errorFieldMapping = <String, String?>{}.obs;
  String validateFilePath(String field, String value, List<String> files) {
    if (value.isEmpty) {
      if (files.isEmpty) {
        return 'anki.word_card.please_select_file'.tr;
      }
    }
    final path = PathUtils(value);
    if (!path.exists()) {
      return 'anki.word_card.file_path_not_exist'.tr;
    }
    if (!path.isFile) {
      return 'anki.word_card.please_select_file_not_directory'.tr;
    }
    return '';
  }
}

class WordCardFormController extends GetxController {
  // 已有数据
  final ankiConnectController = Get.find<AnkiConnectController>();
  final tabController = ShadTabsController(value: 'cloze');
  final sepList = [
    {"label": "---", "value": "---"},
  ];
  final clozeModeList = [
    {
      "label": "anki.word_card.mask_one_guess_one".tr,
      "value": "mask_one_guess_one"
    },
    {
      "label": "anki.word_card.mask_all_guess_one".tr,
      "value": "mask_all_guess_one"
    },
    {
      "label": "anki.word_card.mask_all_guess_all".tr,
      "value": "mask_all_guess_all"
    },
    // {"label": "遮半猜一", "value": "mask_half_guess_one"},
    {"label": "anki.word_card.free_guess".tr, "value": "free_guess"},
  ].obs;
  final clozeGrammarList = [
    {"label": "anki.word_card.bold".tr, "value": "bold"},
    {"label": "anki.word_card.italic".tr, "value": "italic"},
    {"label": "anki.word_card.underline".tr, "value": "underline"},
    {"label": "anki.word_card.strikethrough".tr, "value": "strikethrough"},
    {"label": "anki.word_card.text_color_option".tr, "value": "text_color"},
    {"label": "anki.word_card.text_highlight".tr, "value": "text_highlight"},
  ].obs;
  final commonColorList = [
    {"label": 'anki.word_card.red_color'.tr, "value": "#ff0000"},
    {"label": 'anki.word_card.green_color'.tr, "value": "#00ff00"},
    {"label": 'anki.word_card.blue_color'.tr, "value": "#0000ff"},
    {"label": 'anki.word_card.yellow_color'.tr, "value": "#ffff00"},
    {"label": "yellow", "value": "yellow"},
    {"label": "green", "value": "green"},
    {"label": "cyan", "value": "cyan"},
    {"label": "magenta", "value": "magenta"},
    {"label": "blue", "value": "blue"},
    {"label": "red", "value": "red"},
  ].obs;
  final qaModelList = [
    {
      "label": "anki.word_card.question_answer_same_file".tr,
      "value": "same_file"
    },
    {
      "label": "anki.word_card.question_answer_cross_file".tr,
      "value": "cross_file"
    },
  ].obs;
  final choiceModeList = [
    {
      "label": "anki.word_card.answer_in_question".tr,
      "value": "answer_in_question"
    },
    {
      "label": "anki.word_card.answer_after_option".tr,
      "value": "answer_after_option"
    },
    {
      "label": "anki.word_card.question_answer_cross_file_choice".tr,
      "value": "question_answer_cross_file"
    },
  ].obs;
  final docTypeList = [
    {"label": "TXT", "value": "txt"},
    {"label": "Markdown", "value": "md"},
  ].obs;
  final sepModeList = [
    {"label": 'anki.word_card.normal_separation'.tr, "value": "string"},
    {"label": 'anki.word_card.regex_separation'.tr, "value": "regex"},
  ].obs;
  final subDeckPrefixList = [
    {"label": "@@@", "value": "@@@"},
  ].obs;
  final commonRegexList = [
    {"label": 'anki.word_card.none'.tr, "value": ""},
    {"label": "---", "value": "---"},
    {"label": "1[.．、] xx", "value": "^\\s*\\d+\\s*[.．、]"},
    {"label": "1 xx", "value": "^\\s*\\d+"},
    {"label": "一[、.．] xx", "value": "^\\s*[一二三四五六七八九十]+[.．、]"},
    {"label": "[a-zA-Z]+", "value": "^\\s*[a-zA-Z]+"},
    {"label": "中文字符集", "value": "^\\s*[\u4e00-\u9fff]+"},
    {"label": "问: xx", "value": "^\\s*问[:：]"},
    {"label": "问题: xx", "value": "^\\s*问题[:：]"},
    {"label": "答: xx", "value": "^\\s*答[:：]"},
    {"label": "答案: xx", "value": "^\\s*答案[:：]"},
    {"label": "【答案】 xx", "value": "^\\s*【答案】"},
    {"label": "【答案解析】 xx", "value": "^\\s*【答案解析】"},
    {"label": "【正确答案】 xx", "value": "^\\s*【正确答案】"},
    {"label": "解析: xx", "value": "^\\s*解析[:：]"},
    {"label": "提示: xx", "value": "^\\s*提示[:：]"},
    {"label": "Q: xx", "value": "^\\s*Q[:：]"},
    {"label": "Q1: xx", "value": "^\\s*Q\\d+[:：]"},
    {"label": "A: xx", "value": "^\\s*A[:：]"},
    {"label": "A1: xx", "value": "^\\s*A\\d+[:：]"},
    {"label": "h: xx", "value": "^\\s*h[:：]"},
    {"label": "* xx", "value": "^\\s*\\*"},
    {"label": "- xx", "value": "^\\s*-"},
    {"label": "+ xx", "value": "^\\s*\\+"},
    {"label": "> xx", "value": "^\\s*>"},
    {"label": "# xx", "value": "^\\s*#\\s"},
    {"label": "## xx", "value": "^\\s*##\\s"},
    {"label": "### xx", "value": "^\\s*###\\s"},
    {"label": "#### xx", "value": "^\\s*####\\s"},
    {"label": "(1) xx", "value": "^\\s*[\\(（]\\d+[\\)）]"},
    {"label": "1) xx", "value": "^\\s*\\d+[\\)）])"},
    {"label": "A[.．、] xx", "value": "^\\s*[A-Z]+[.．、]"},
    {"label": "(a) xx", "value": "^\\s*[\\(（][a-z]+[\\)）]"},
    {"label": "a) xx", "value": "^\\s*[a-z]+[\\)）]"},
  ];
  final questionRegexList = [
    {"label": "---", "value": "---"},
    {"label": "1[.．、] xx", "value": "^\\s*\\d+\\s*[.．、]"},
    {"label": "1 xx", "value": "^\\s*\\d+"},
    {"label": "* xx", "value": "^\\s*\\*"},
    {"label": "- xx", "value": "^\\s*-"},
    {"label": "+ xx", "value": "^\\s*\\+"},
    {"label": "> xx", "value": "^\\s*>"},
    {"label": "# xx", "value": "^\\s*#\\s"},
    {"label": "## xx", "value": "^\\s*##\\s"},
    {"label": "### xx", "value": "^\\s*###\\s"},
    {"label": "#### xx", "value": "^\\s*####\\s"},
    {"label": "Q: xx", "value": "^\\s*Q[:：]"},
    {"label": "Q1: xx", "value": "^\\s*Q\\d+[:：]"},
    {"label": "问: xx", "value": "^\\s*问[:：]"},
    {"label": "问题: xx", "value": "^\\s*问题[:：]"},
    {"label": "一[、.．] xx", "value": "^\\s*[一二三四五六七八九十]+[.．、]"},
    {"label": "(1) xx", "value": "^\\s*[\\(（]\\d+[\\)）]"},
    {"label": "1) xx", "value": "^\\s*\\d+[\\)）])"},
    {"label": "A[.．、] xx", "value": "^\\s*[A-Z]+[.．、]"},
    {"label": "(a) xx", "value": "^\\s*[\\(（][a-z]+[\\)）]"},
    {"label": "a) xx", "value": "^\\s*[a-z]+[\\)）]"},
    {"label": "[a-zA-Z]+", "value": "^\\s*[a-zA-Z]+"},
    {"label": "[一二三四五六七八九十]+", "value": "^\\s*[一二三四五六七八九十]+"},
    {"label": "中文字符集", "value": "^\\s*[\u4e00-\u9fff]+"},
  ];
  final answerRegexList = [
    {"label": 'anki.word_card.none'.tr, "value": ""},
    {"label": "---", "value": "---"},
    {"label": "答案: xx", "value": "^\\s*答案"},
    {"label": "正确答案: xx", "value": "^\\s*正确答案"},
    {"label": "【答案】 xx", "value": "^\\s*【答案】"},
    {"label": "【正确答案】 xx", "value": "^\\s*【正确答案】"},
    {"label": "【答案解析】 xx", "value": "^\\s*【答案解析】"},
    {"label": "1[.．、]?[A-G]+", "value": "\\d+[.．、]?\\s*[A-G\\s]+"},
    {"label": "1[.．、][A-G]+", "value": "\\d+[.．、]\\s*[A-G\\s]+"},
    {"label": "1 [A-G]+", "value": "\\d+\\s*[A-G\\s]+"},
    {"label": "([A-G]+)", "value": "[\(（](\\s*[A-G\\s]+\\s*)[\)）]"},
    {"label": "[A-G]+", "value": "([A-G\\s]+)"},
    {"label": "[a-zA-Z]+", "value": "^\\s*[a-zA-Z]+"},
    {"label": "中文字符集", "value": "^\\s*[\u4e00-\u9fff]+"},
  ];
  final qaAnswerRegexList = [
    {"label": 'anki.word_card.none'.tr, "value": ""},
    {"label": "---", "value": "---"},
    {"label": "1[.．、] xx", "value": "^\\s*\\d+\\s*[.．、]"},
    {"label": "答: xx", "value": "^\\s*答\\s*[:：]"},
    {"label": "A: xx", "value": "^\\s*A\\s*[:：]"},
    {"label": "答案: xx", "value": "^\\s*答案"},
    {"label": "正确答案: xx", "value": "^\\s*正确答案"},
    {"label": "【答案】 xx", "value": "^\\s*【答案】"},
    {"label": "【正确答案】 xx", "value": "^\\s*【正确答案】"},
    {"label": "【答案解析】 xx", "value": "^\\s*【答案解析】"},
    {"label": "中文字符集", "value": "^\\s*[\u4e00-\u9fff]+"},
  ];

  final choiceAnswerRegexList = [
    {"label": "---", "value": "---"},
    {"label": "答案: xx", "value": "\\s*答案[:：]"},
    {"label": "【答案】xx", "value": "\\s*【答案】"},
    {"label": "【正确答案】 xx", "value": "\\s*【正确答案】"},
    {"label": "【答案解析】 xx", "value": "\\s*【答案解析】"},
    {"label": "1[.．、][A-G]+", "value": "\\d+[.．、]\\s*[A-G\\s]+"},
    {"label": "1[.．、] 答案：[A-G]+", "value": "\\d+[.．、]\\s*答案[:：]\\s*[A-G\\s]+"},
    {"label": "1[.．、]?[A-G]+", "value": "\\d+[.．、]?\\s*[A-G\\s]+"},
    {"label": "([A-G]+)", "value": "[\(（](\\s*[A-G\\s]+\\s*)[\)）]"},
    {"label": "[A-G]+", "value": "([A-G\\s]+)"},
  ];
  final optionRegexList = [
    {"label": "[A-G][.．、]", "value": "[A-G]\\s*[.．、]"},
    {"label": "[A-G][.．、：:]", "value": "[A-G]\\s*[.．、：:]"},
    {"label": "[A-G][.．、]?", "value": "[A-G]\\s*[.．、]?"},
    {"label": "[A-G][.．、：:]?", "value": "[A-G]\\s*[.．、：:]?"},
  ];
  final remarkRegexList = [
    {"label": 'anki.word_card.none'.tr, "value": ""},
    {"label": "解析：xx", "value": "\\s*解析[:：]"},
    {"label": "【解析】xx", "value": "\\s*【解析】"},
    {"label": "【答案解析】xx", "value": "\\s*【答案解析】"},
  ];
  final hintRegexList = [
    {"label": 'anki.word_card.none'.tr, "value": ""},
    {"label": "提示: xx", "value": "^\\s*提示[:：]"},
    {"label": "h: xx", "value": "^\\s*h[:：]"},
  ];
  final judgeAnswerList = <Map<String, String>>[
    {
      "label": "((√|Y|是|对|正确|T|True|Yes)|(x|N|否|错误|错|F|False|No))",
      "value":
          "[(（]\\s*((√|Y|是|对|正确|T|True|Yes)|(x|×|N|否|错误|错|F|False|No))\\s*[)）]"
    },
    {
      "label": "答案: ((√|Y|是|对|正确|T|True|Yes)|(x|N|否|错误|错|F|False|No))",
      "value":
          "答案[:：]\\s*((√|Y|是|对|正确|T|True|Yes)|(x|×|N|否|错误|错|F|False|No))\\s*"
    },
    {
      "label": "【答案】((√|Y|是|对|正确|T|True|Yes)|(x|N|否|错误|错|F|False|No))",
      "value": "【答案】\\s*((√|Y|是|对|正确|T|True|Yes)|(x|×|N|否|错误|错|F|False|No))\\s*"
    },
    {
      "label": "1[.．、] (√|Y|是|对|正确|T|True|Yes)|(x|N|否|错误|错|F|False|No)",
      "value":
          "\\d+[.．、]\\s*((√|Y|是|对|正确|T|True|Yes)|(x|×|N|否|错误|错|F|False|No))\\s*"
    },
    {
      "label": "1[.．、] 答案: (√|Y|是|对|正确|T|True|Yes)|(x|N|否|错误|错|F|False|No)",
      "value":
          "\\d+[.．、]\\s*答案[:：]\\s*((√|Y|是|对|正确|T|True|Yes)|(x|×|N|否|错误|错|F|False|No))\\s*"
    },
    {
      "label": "1[.．、]【答案】(√|Y|是|对|正确|T|True|Yes)|(x|N|否|错误|错|F|False|No)",
      "value":
          "\\d+[.．、]\\s*【答案】\\s*((√|Y|是|对|正确|T|True|Yes)|(x|×|N|否|错误|错|F|False|No))\\s*"
    },
  ].obs;

  // 表单参数
  final parentDeck = 'anki.word_card.guru_import'.tr.obs;
  final isCreateSubDeck = false.obs;
  final subDeckPrefix = "@@@".obs;
  final cardModel = "Kevin Text Cloze v3".obs;
  final docType = "txt".obs;
  final isObsidian = false.obs;
  final mediaFolder = "".obs;
  final qMainFieldList = <String>[].obs;
  final aMainFieldList = <String>[].obs;
  final isUseInternalModel = false.obs;
  final correctRegex = "".obs;
  final wrongRegex = "".obs;
  final isUseSep = false.obs;
  final sep = "".obs;
  final tags = <String>[].obs;
  final fieldMappings = <String, PatternMatch>{}.obs;
  final selectedFilePaths = <String>[].obs;
  final qFile = "".obs;
  final aFile = "".obs;
  final textColorList = <String>[].obs;
  final highlightColorList = <String>[].obs;
  final clozeParams = Get.put(ClozeParams());
  final qaParams = Get.put(QaParams());
  final choiceParams = Get.put(ChoiceParams());
  final judgeParams = Get.put(JudgeParams());
  // 控制器
  final progressController = Get.find<ProgressController>();
  final messageController = Get.find<MessageController>();
  final settingController = Get.find<SettingController>();
  final webviewController = Get.find<WebviewController>();

  @override
  void onInit() async {
    super.onInit();
    if (ankiConnectController.fieldList.isNotEmpty) {
      qMainFieldList.value = [ankiConnectController.fieldList[0]];
      aMainFieldList.value = [ankiConnectController.fieldList[0]];
    }
    if (ankiConnectController.parentDeckList.isNotEmpty) {
      parentDeck.value = ankiConnectController.parentDeckList[0];
    }
    if (subDeckPrefixList.isNotEmpty) {
      subDeckPrefix.value = subDeckPrefixList.first['value']!;
    }
    await updateFieldList(cardModel.value);
  }

  @override
  void onClose() {
    clozeParams.dispose();
    qaParams.dispose();
    choiceParams.dispose();
    judgeParams.dispose();
    super.onClose();
  }

  Future<void> updateFieldList(String modelName) async {
    try {
      // 调用updateFieldList并等待它完成，但不使用返回值
      await ankiConnectController.updateFieldList(modelName);
      fieldMappings.clear();

      // 为新字段创建映射，保留已有的映射
      for (var field in ankiConnectController.fieldList) {
        if (tabController.selected == "cloze") {
          if (field == "Front") {
            fieldMappings[field] =
                PatternMatch(field, "^\\s*\\d+\\s*[.．、]", null, true);
          } else {
            fieldMappings[field] = PatternMatch(field, "", null, true);
          }
        } else if (tabController.selected == "qa") {
          if (field == "Front") {
            fieldMappings[field] =
                PatternMatch(field, "^\\s*\\d+\\s*[.．、]", null, true);
          } else {
            fieldMappings[field] = PatternMatch(field, "", null, true);
          }
        } else if (tabController.selected == "choice") {
          if (field == "Question") {
            fieldMappings[field] =
                PatternMatch(field, "^\\s*\\d+\\s*[.．、]", null, true);
          } else if (field == "Options") {
            fieldMappings[field] =
                PatternMatch(field, "[A-G]\\s*[.．、]", null, true);
          } else if (field == "Answers") {
            if (choiceParams.mode == 'answer_in_question') {
              fieldMappings[field] = PatternMatch(
                  field, "[\(（](\\s*[A-G\\s]+\\s*)[\)）]", null, true);
            } else {
              fieldMappings[field] =
                  PatternMatch(field, "\\s*答案[:：]", null, true);
            }
          } else {
            fieldMappings[field] = PatternMatch(field, "", null, true);
          }
        } else if (tabController.selected == "judge") {
          if (field == "Question") {
            fieldMappings[field] =
                PatternMatch(field, "^\\s*\\d+\\s*[.．、]", null, true);
          } else if (field == "Answers") {
            fieldMappings[field] = PatternMatch(
                field,
                "[(（]\\s*((√|Y|是|对|正确|T|True|Yes)|(x|×|N|否|错误|错|F|False|No))\\s*[)）]",
                null,
                true);
          } else {
            fieldMappings[field] = PatternMatch(field, "", null, true);
          }
        } else {
          fieldMappings[field] = PatternMatch(field, "", null, true);
        }
        if (ankiConnectController.fieldList.isNotEmpty) {
          qMainFieldList.value = [ankiConnectController.fieldList[0]];
          aMainFieldList.value = [ankiConnectController.fieldList[0]];
        }

        // 更新字段映射相关的UI
        final updateId = 'field_mappings_${field}_$modelName';
        update([updateId]);
      }

      // 更新主控制器状态
      update();
    } catch (e, stack) {
      logger.e("updateFieldList error", error: e, stackTrace: stack);
    }
  }

  // 获取字段映射值
  PatternMatch? getFieldMappingValue(String field) {
    if (fieldMappings.containsKey(field)) {
      return fieldMappings[field];
    }
    return null;
  }

  // 更新字段映射
  void updateFieldMapping(String field, PatternMatch value) {
    fieldMappings[field] = value;
    fieldMappings.refresh();
    update();
  }

  // 提交表单
  Future<void> submit(context) async {
    try {
      progressController.updateProgress(
        status: "running",
        message: 'progress.status.processing'.tr,
        current: 1.0,
        total: 100.0,
      );
      if (tabController.selected == "cloze") {
        await _handleClozeCard(context);
      } else if (tabController.selected == "qa") {
        await _handleQACard(context);
      } else if (tabController.selected == "choice") {
        await _handleChoiceCard(context);
      } else if (tabController.selected == "judge") {
        await _handleJudgeCard(context);
      }
    } catch (e, stackTrace) {
      logger.e("submit error", error: e, stackTrace: stackTrace);
      progressController.updateProgress(
        status: "error",
        message: e.toString(),
      );
    }
  }

  /// 处理挖空题制卡
  Future<void> _handleClozeCard(BuildContext context) async {
    _initProgressController(context);
    if (cardModel.value != "Kevin Text Cloze v3") {
      progressController.updateProgress(
        status: "error",
        message: 'anki.word_card.please_select_kevin_text_cloze_template'.tr,
      );
      return;
    }
    _validateFieldPattern(
        'Front', 'anki.word_card.please_set_front_field_pattern'.tr);

    // 验证文件选择
    _validateFileSelection();

    final notes = <AnkiNote>[];
    Map<String, String> fieldPatterns = {};
    for (final mapping in fieldMappings.entries) {
      fieldPatterns[mapping.key] = mapping.value.regex;
    }
    final tempDir = await PathUtils.tempDir;
    final allImagePaths = <String>[];
    for (final filePath in selectedFilePaths) {
      progressController.updateProgress(
        status: "running",
        message: 'anki.word_card.convert_to_html'.tr,
        current: 10,
        total: 100,
      );
      final rawHtml = await AnkiConnectController()
          .docx2html(filePath, escapeLatexForJs: false);
      final (html, imagePaths) =
          await replaceHtmlBase64Img(rawHtml, tempDir, htmlType: "document");
      allImagePaths.addAll(imagePaths);
      logger.i("allImagePaths: $allImagePaths");
      final htmlMap = await webviewController.parseHtmlToCards(
          html,
          fieldPatterns,
          parentDeck.value,
          isCreateSubDeck.value ? subDeckPrefix.value : "");
      logger.i("htmlMap: $htmlMap");
      progressController.updateProgress(
        status: "running",
        message: 'anki.word_card.extract_card'.tr,
        current: 30,
        total: 100,
      );
      for (final entry in htmlMap.entries) {
        final deckName = entry.key;
        final objs = entry.value;
        for (final obj in objs) {
          final front = await webviewController.convertCloze(
              obj['Front'] ?? '',
              clozeParams.clozeGrammar.value,
              textColorList.value,
              highlightColorList.value);
          final back = await webviewController.convertCloze(
              obj['Back'] ?? '',
              clozeParams.clozeGrammar.value,
              textColorList.value,
              highlightColorList.value);
          if ((clozeParams.mode.value == "mask_one_guess_one" ||
                  clozeParams.mode.value == "mask_all_guess_one") &&
              clozeParams.isPerClozePerCard.value) {
            final RegExp clozeRegex = RegExp(r'\[\[c\d+::(.*?)\]\]');
            final Iterable<Match> matches = clozeRegex.allMatches(front);
            final uid = Ulid().toString();
            for (int i = 0; i < matches.length; i++) {
              obj['ID'] = '${uid}_${i + 1}';
              obj['Mode'] = '${clozeParams.mode.value}_multi';
              obj['Front'] = front;
              obj['Back'] = back;
              obj['Index'] = "c${i + 1}";
              // 根据fieldMappings，将obj中对应字段内容判断是否删除前缀
              for (final mapping in fieldMappings.entries) {
                final field = mapping.key;
                final pattern = mapping.value;
                if (!pattern.keepPrefix &&
                    obj.containsKey(field) &&
                    obj[field] != null) {
                  final content = obj[field]!;
                  final regExp = RegExp(pattern.regex, multiLine: true);
                  final match = regExp.firstMatch(content);
                  if (match != null) {
                    // 删除前缀，保留后面的内容
                    obj[field] = content.substring(match.end).trimLeft();
                  }
                }
              }
              AnkiNote note = _createNote(deckName, obj, cardModel.value);
              notes.add(note);
            }
          } else {
            obj['ID'] = Ulid().toString();
            obj['Mode'] = clozeParams.mode.value;
            obj['Front'] = front;
            obj['Back'] = back;
            // 根据fieldMappings，将obj中对应字段内容判断是否删除前缀
            for (final mapping in fieldMappings.entries) {
              final field = mapping.key;
              final pattern = mapping.value;
              if (!pattern.keepPrefix &&
                  obj.containsKey(field) &&
                  obj[field] != null) {
                final content = obj[field]!;
                final regExp = RegExp(pattern.regex, multiLine: true);
                final match = regExp.firstMatch(content);
                if (match != null) {
                  // 删除前缀，保留后面的内容
                  obj[field] = content.substring(match.end).trimLeft();
                }
              }
            }
            AnkiNote note = _createNote(deckName, obj, cardModel.value);
            notes.add(note);
          }
        }
      }
    }
    // 生成并导入卡片
    await AnkiConnectController()
        .generateAndImportCards(notes, mediaList: allImagePaths);
  }

  /// 处理问答题制卡
  Future<void> _handleQACard(BuildContext context) async {
    _initProgressController(context);

    if (qaParams.mode.value == "same_file") {
      await _handleQASameFileMode();
    } else if (qaParams.mode.value == "cross_file") {
      await _handleQACrossFileMode();
    }
  }

  /// 处理选择题制卡
  Future<void> _handleChoiceCard(BuildContext context) async {
    _initProgressController(context);

    if (cardModel.value != "Kevin Choice Card v2") {
      progressController.updateProgress(
        status: "error",
        message: 'anki.word_card.please_select_kevin_choice_template'.tr,
      );
      return;
    }

    if (choiceParams.mode.value != "question_answer_cross_file") {
      await _handleChoiceSameFileMode();
    } else {
      await _handleChoiceCrossFileMode();
    }
  }

  /// 处理判断题制卡
  Future<void> _handleJudgeCard(BuildContext context) async {
    _initProgressController(context);

    if (cardModel.value != "Kevin Choice Card v2") {
      progressController.updateProgress(
        status: "error",
        message: 'anki.word_card.please_select_kevin_choice_template'.tr,
      );
      return;
    }

    if (judgeParams.mode.value != "cross_file") {
      await _handleJudgeSameFileMode();
    } else {
      await _handleJudgeCrossFileMode();
    }
  }

  /// 处理文本提取
  Map<String, String> _extractContent(String text, List<PatternMatch> matches) {
    final extractedContent = <String, String>{};

    // 按位置排序
    matches
        .sort((a, b) => (a.match?.start ?? 0).compareTo(b.match?.start ?? 0));

    for (var i = 0; i < matches.length; i++) {
      final current = matches[i];
      final nextMatch = i < matches.length - 1 ? matches[i + 1] : null;
      final nextStart = nextMatch?.match?.start ?? text.length;

      if (tabController.selected == "choice") {
        if (current.field == "Options") {
          final optionText = text
              .substring(
                  current.keepPrefix
                      ? current.match!.start
                      : current.match!.end,
                  nextStart)
              .trim();
          // 去除选项标记（如A、B、C等）
          final cleanedOption =
              optionText.replaceAll(RegExp(r'^[A-G][.．、：:]\s*'), '');
          final existOptions = extractedContent[current.field];
          if (existOptions != null && existOptions.isNotEmpty) {
            extractedContent[current.field] = '$existOptions||$cleanedOption';
          } else {
            extractedContent[current.field] = cleanedOption;
          }
          continue;
        } else if (current.field == "Answers") {
          // 处理答案：转换ABCD为1234
          final answerText = text
              .substring(
                  current.keepPrefix
                      ? current.match!.start
                      : current.match!.end,
                  nextStart)
              .trim();
          final answerMatch = RegExp(r'[A-G\s]+').firstMatch(answerText);
          if (answerMatch != null) {
            String answers = '';
            for (var ch in answerMatch.group(0)!.split('')) {
              if ('A'.codeUnitAt(0) <= ch.codeUnitAt(0) &&
                  ch.codeUnitAt(0) <= 'Z'.codeUnitAt(0)) {
                if (answers.isNotEmpty) {
                  answers += '||';
                }
                answers += '${ch.codeUnitAt(0) - 'A'.codeUnitAt(0) + 1}';
              }
            }
            extractedContent[current.field] = answers;
          }
          continue;
        }
      } else if (tabController.selected == "judge") {
        if (current.field == "Answers") {
          // 处理答案：根据正则分组判断答案
          final answerText = text
              .substring(
                  current.keepPrefix
                      ? current.match!.start
                      : current.match!.end,
                  nextStart)
              .trim();
          // 使用完整的正则表达式匹配
          final regExp = RegExp(current.regex);
          final match = regExp.firstMatch(answerText);
          if (match != null) {
            // 如果第二个分组有匹配（正确答案组），则答案为1
            // 如果第三个分组有匹配（错误答案组），则答案为2
            final isCorrect = match.group(2) != null;
            final isWrong = match.group(3) != null;
            if (isCorrect) {
              extractedContent[current.field] = "1";
            } else if (isWrong) {
              extractedContent[current.field] = "2";
            } else {
              extractedContent[current.field] = '';
            }
          } else {
            // 如果没有匹配到任何答案，设置为空字符串
            extractedContent[current.field] = '';
          }
        } else {
          final content = text
              .substring(
                  current.keepPrefix
                      ? current.match!.start
                      : current.match!.end,
                  nextStart)
              .trim();
          extractedContent[current.field] = content;
        }
        continue;
      }
      final content = text
          .substring(
              current.keepPrefix ? current.match!.start : current.match!.end,
              nextStart)
          .trim();
      extractedContent[current.field] = content;
    }

    return extractedContent;
  }

  String getFirstField(String path, {List<String>? roiFields}) {
    final text = File(path).readAsStringSync();
    final matches = <PatternMatch>[];
    for (final mapping in fieldMappings.entries) {
      final field = mapping.key;
      final pattern = mapping.value;
      if (pattern.regex.isEmpty) continue;
      if (roiFields != null && !roiFields.contains(field)) continue;
      final regExp = RegExp(pattern.regex, multiLine: true);
      final match = regExp.firstMatch(text);
      if (match != null) {
        matches
            .add(PatternMatch(field, pattern.regex, match, pattern.keepPrefix));
      }
    }
    if (matches.isEmpty) {
      return "";
    }
    matches
        .sort((a, b) => (a.match?.start ?? 0).compareTo(b.match?.start ?? 0));
    return matches.first.field;
  }

  /// 处理问答题同文件模式
  Future<void> _handleQASameFileMode() async {
    _validateFileSelection();
    // _validateFieldPattern('Front', '请设置Front字段的匹配模式!');
    final notes = <AnkiNote>[];
    final webviewController = Get.find<WebviewController>();
    Map<String, String> fieldPatterns = {};
    for (final mapping in fieldMappings.entries) {
      fieldPatterns[mapping.key] = mapping.value.regex;
    }
    final tempDir = await PathUtils.tempDir;
    final allImagePaths = <String>[];
    for (final filePath in selectedFilePaths) {
      final rawHtml = await AnkiConnectController().docx2html(filePath);
      final (html, imagePaths) =
          await replaceHtmlBase64Img(rawHtml, tempDir, htmlType: "document");
      allImagePaths.addAll(imagePaths);
      final htmlMap = await webviewController.parseHtmlToCards(
          html,
          fieldPatterns,
          parentDeck.value,
          isCreateSubDeck.value ? subDeckPrefix.value : "");
      progressController.updateProgress(
        status: "running",
        message: 'anki.word_card.extract_card'.tr,
        current: 30,
        total: 100,
      );
      for (final entry in htmlMap.entries) {
        final deckName = entry.key;
        final objs = entry.value;
        for (final obj in objs) {
          if (cardModel.value == "Kevin Text QA Card v2") {
            if (qaParams.isQuestionSingleLine.value) {
              final parts =
                  await webviewController.parseHtmlToParts(obj['Front'] ?? '');
              if (parts.isNotEmpty) {
                obj['Front'] = parts.first;
                obj['Back'] = parts.skip(1).join('\n');
              }
            }
            if (qaParams.isAnswerCloze.value) {
              obj['Back'] = await webviewController.convertCloze(
                  obj['Back'] ?? '',
                  qaParams.clozeGrammar.value,
                  textColorList.value,
                  highlightColorList.value);
            }
          }
          // 根据fieldMappings，将obj中对应字段内容判断是否删除前缀
          for (final mapping in fieldMappings.entries) {
            final field = mapping.key;
            final pattern = mapping.value;
            if (!pattern.keepPrefix &&
                obj.containsKey(field) &&
                obj[field] != null) {
              final content = obj[field]!;
              final regExp = RegExp(pattern.regex, multiLine: true);
              final match = regExp.firstMatch(content);
              if (match != null) {
                // 删除前缀，保留后面的内容
                obj[field] = content.substring(match.end).trimLeft();
              }
            }
          }
          final note = _createNote(deckName, obj, cardModel.value);
          notes.add(note);
        }
      }
    }
    await AnkiConnectController()
        .generateAndImportCards(notes, mediaList: allImagePaths);
  }

  /// 处理问答题跨文件模式
  Future<void> _handleQACrossFileMode() async {
    if (qFile.isEmpty || aFile.isEmpty) {
      progressController.updateProgress(
        status: "error",
        message: 'anki.word_card.please_select_question_answer_files'.tr,
      );
      return;
    }

    final tempDir = await PathUtils.tempDir;
    final webviewController = Get.find<WebviewController>();
    final allImagePaths = <String>[];
    // 处理问题文件
    final rawHtml = await AnkiConnectController().docx2html(qFile.value);
    final (html, imagePaths) =
        await replaceHtmlBase64Img(rawHtml, tempDir, htmlType: "document");
    allImagePaths.addAll(imagePaths);
    final text = await webviewController.htmlToText(html);
    final qTempFilePath = await PathUtils.getTempFilePath(".txt");
    File(qTempFilePath).writeAsStringSync(text);
    final questionField = getFirstField(qTempFilePath);
    final questionPattern = fieldMappings[questionField]!.regex;

    final textMapQuestion = await AnkiConnectController().splitText(
      qTempFilePath,
      questionPattern,
      parentDeck.value,
      isCreateSubDeck.value ? subDeckPrefix.value : null,
    );
    // 处理答案文件
    final rawHtmlAnswer = await AnkiConnectController().docx2html(aFile.value);
    final (htmlAnswer, imagePathsAnswer) = await replaceHtmlBase64Img(
        rawHtmlAnswer, tempDir,
        htmlType: "document");
    allImagePaths.addAll(imagePathsAnswer);
    final textAnswer = await webviewController.htmlToText(htmlAnswer);
    final aTempFilePath = await PathUtils.getTempFilePath(".txt");
    File(aTempFilePath).writeAsStringSync(textAnswer);
    final answerField = getFirstField(aTempFilePath);
    final answerPattern = fieldMappings[answerField]!.regex;

    final textMapAnswer = await AnkiConnectController().splitText(
      aTempFilePath,
      answerPattern,
      parentDeck.value,
      isCreateSubDeck.value ? subDeckPrefix.value : null,
    );
    progressController.updateProgress(
      status: "running",
      message: 'anki.word_card.extract_card'.tr,
      current: 30,
      total: 100,
    );
    final notes = <AnkiNote>[];
    final totalQuestions =
        textMapQuestion.values.fold<int>(0, (sum, list) => sum + list.length);
    var currentQuestion = 0;

    for (var entry in textMapQuestion.entries) {
      final deckName = entry.key;
      final questionTexts = entry.value;
      final answerTexts = textMapAnswer[deckName] ?? [];

      if (questionTexts.length != answerTexts.length) {
        progressController.updateProgress(
          status: "error",
          message: 'anki.word_card.question_answer_count_mismatch'.trParams({
            'deck': deckName,
            'questionCount': questionTexts.length.toString(),
            'answerCount': answerTexts.length.toString()
          }),
        );
        return;
      }

      for (var i = 0; i < questionTexts.length; i++) {
        final qText = questionTexts[i];
        final aText = answerTexts[i];
        final extractedContent = <String, String>{};
        final qMatches = <PatternMatch>[];
        final aMatches = <PatternMatch>[];

        for (final mapping in fieldMappings.entries) {
          final field = mapping.key;
          final pattern = mapping.value;
          if (pattern.regex.isEmpty) continue;
          if (qMainFieldList.contains(field)) {
            final regExp = RegExp(pattern.regex, multiLine: true);
            final qMatch = regExp.firstMatch(qText);

            if (qMatch != null) {
              qMatches.add(PatternMatch(
                  field, pattern.regex, qMatch, pattern.keepPrefix));
            }
          }
          if (aMainFieldList.contains(field)) {
            final regExp = RegExp(pattern.regex, multiLine: true);
            final aMatch = regExp.firstMatch(aText);
            if (aMatch != null) {
              aMatches.add(PatternMatch(
                  field, pattern.regex, aMatch, pattern.keepPrefix));
            }
          }
        }

        final qContent = _extractContent(qText, qMatches);
        final aContent = _extractContent(aText, aMatches);
        for (var entry in qContent.entries) {
          extractedContent[entry.key] = entry.value;
        }
        for (var entry in aContent.entries) {
          if (!extractedContent.containsKey(entry.key)) {
            extractedContent[entry.key] = entry.value;
          }
        }
        final note = _createNote(deckName, extractedContent, cardModel.value);
        notes.add(note);
        currentQuestion++;
        progressController.updateProgress(
          status: "running",
          message: 'anki.word_card.processing_question'
              .trParams({'current': currentQuestion.toString()}),
          current: currentQuestion.toDouble(),
          total: totalQuestions.toDouble(),
        );
      }
    }
    await AnkiConnectController()
        .generateAndImportCards(notes, mediaList: allImagePaths);
  }

  /// 验证文件选择
  void _validateFileSelection() {
    if (selectedFilePaths.isEmpty) {
      throw Exception('anki.word_card.please_select_files'.tr);
    }
  }

  /// 验证字段匹配模式
  String _validateFieldPattern(String field, String errorMessage) {
    final pattern = fieldMappings[field]?.regex;
    if (pattern == null || pattern.isEmpty) {
      throw Exception(errorMessage);
    }
    return pattern;
  }

  /// 创建笔记
  AnkiNote _createNote(
      String deckName, Map<String, String> content, String modelName) {
    final fields =
        List.generate(ankiConnectController.fieldList.length, (index) {
      final field = ankiConnectController.fieldList[index];
      return content[field] ?? '';
    });
    return AnkiNote(
      deckName: deckName,
      modelName: modelName,
      fields: fields,
      tags: tags,
    );
  }

  /// 处理选择题同文件模式
  Future<void> _handleChoiceSameFileMode() async {
    _validateFileSelection();
    final questionPattern = _validateFieldPattern(
        'Question', 'anki.word_card.please_set_question_field_pattern'.tr);
    final controller = Get.find<WordCardFormController>();
    final notes = <AnkiNote>[];
    final webviewController = Get.find<WebviewController>();
    Map<String, String> fieldPatterns = {};
    for (final mapping in fieldMappings.entries) {
      fieldPatterns[mapping.key] = mapping.value.regex;
    }
    final tempDir = await PathUtils.tempDir;
    final allImagePaths = <String>[];
    for (final filePath in selectedFilePaths) {
      final rawHtml = await AnkiConnectController().docx2html(filePath);
      final (html, imagePaths) =
          await replaceHtmlBase64Img(rawHtml, tempDir, htmlType: "document");
      allImagePaths.addAll(imagePaths);
      final text = await webviewController.htmlToText(html);
      // 保存到临时文件
      final tempFilePath = await PathUtils.getTempFilePath(".txt");
      File(tempFilePath).writeAsStringSync(text);
      final textMap = await AnkiConnectController().splitText(
        tempFilePath,
        questionPattern,
        parentDeck.value,
        isCreateSubDeck.value ? subDeckPrefix.value : null,
      );
      final totalQuestions =
          textMap.values.fold<int>(0, (sum, list) => sum + list.length);
      var currentQuestion = 0;
      progressController.updateProgress(
        status: "running",
        message: 'anki.word_card.extract_card'.tr,
        current: 30,
        total: 100,
      );
      for (var entry in textMap.entries) {
        final deckName = entry.key;
        final texts = entry.value;

        for (var text in texts) {
          if (text.isEmpty) {
            continue;
          }
          final matches = <PatternMatch>[];
          for (final mapping in fieldMappings.entries) {
            final field = mapping.key;
            final pattern = mapping.value;
            if (pattern.regex.isEmpty) continue;
            if (controller.choiceParams.mode.value == "answer_in_question" &&
                field == "Answers") {
              continue;
            }
            final regExp = RegExp(pattern.regex, multiLine: true);
            if (field == "Options") {
              final optionMatches = regExp.allMatches(text).toList();

              // 找出Answers和Remarks的位置，用于过滤选项
              int answersPosition = text.length;
              int remarksPosition = text.length;

              if (fieldMappings.containsKey("Answers") &&
                  fieldMappings["Answers"]!.regex.isNotEmpty) {
                final answersRegExp =
                    RegExp(fieldMappings["Answers"]!.regex, multiLine: true);
                final answersMatch = answersRegExp.firstMatch(text);
                if (answersMatch != null) {
                  answersPosition = answersMatch.start;
                }
              }

              if (fieldMappings.containsKey("Remarks") &&
                  fieldMappings["Remarks"]!.regex.isNotEmpty) {
                final remarksRegExp =
                    RegExp(fieldMappings["Remarks"]!.regex, multiLine: true);
                final remarksMatch = remarksRegExp.firstMatch(text);
                if (remarksMatch != null) {
                  remarksPosition = remarksMatch.start;
                }
              }

              for (var optionMatch in optionMatches) {
                if (controller.choiceParams.mode.value ==
                    "answer_after_option") {
                  // 只添加位于答案和解析之前的选项匹配
                  if (optionMatch.start < answersPosition &&
                      optionMatch.start < remarksPosition) {
                    matches.add(
                        PatternMatch(field, pattern.regex, optionMatch, false));
                  }
                } else {
                  if (optionMatch.start < remarksPosition) {
                    matches.add(
                        PatternMatch(field, pattern.regex, optionMatch, false));
                  }
                }
              }
              continue;
            }
            final match = regExp.firstMatch(text);
            if (match != null) {
              matches.add(PatternMatch(
                  field, pattern.regex, match, pattern.keepPrefix));
            }
          }
          final extractedContent = _extractContent(text, matches);
          logger.i("extractedContent: $extractedContent");
          if (controller.choiceParams.mode.value == "answer_in_question") {
            final regExp =
                RegExp(fieldMappings['Answers']!.regex, multiLine: true);
            final match = regExp.firstMatch(extractedContent['Question'] ?? "");
            String answers = '';
            for (var ch in match?.group(0)!.split('') ?? []) {
              if ('A'.codeUnitAt(0) <= ch.codeUnitAt(0) &&
                  ch.codeUnitAt(0) <= 'Z'.codeUnitAt(0)) {
                if (answers.isNotEmpty) {
                  answers += '||';
                }
                answers += '${ch.codeUnitAt(0) - 'A'.codeUnitAt(0) + 1}';
              }
            }
            extractedContent["Answers"] = answers;
            if (extractedContent.containsKey('Question')) {
              extractedContent['Question'] = extractedContent['Question']!
                  .replaceAll(RegExp(fieldMappings['Answers']!.regex), '___');
              logger.w("question: ${extractedContent['Question']}");
            }
          }

          // notes.add(_createNote(deckName, extractedContent, cardModel.value));
          final note = _createNote(deckName, extractedContent, cardModel.value);
          if (docType.value == "md") {
            List<int> fieldIds =
                List.generate(ankiConnectController.fieldList.length, (i) => i);
            String mediaDir = PathUtils(filePath).parent;
            if (mediaFolder.value.isNotEmpty) {
              mediaDir = mediaFolder.value;
            }
            final newNote = await AnkiConnectController().convertMD2HTML(
                note, fieldIds, filePath,
                mediaDir: mediaDir, convertObsidianLinks: isObsidian.value);
            // 把每个字段的最外层p标签去除
            for (var idx = 0; idx < newNote.fields.length; idx++) {
              final fieldContent = newNote.fields[idx];
              newNote.fields[idx] =
                  await AnkiConnectController().removeRootPTag(fieldContent);
            }
            notes.add(newNote);
          } else {
            notes.add(note);
          }

          currentQuestion++;
          progressController.updateProgress(
            status: "running",
            message: 'anki.word_card.processing_question'
                .trParams({'current': currentQuestion.toString()}),
            current: currentQuestion.toDouble(),
            total: totalQuestions.toDouble(),
          );
        }
      }
    }
    await AnkiConnectController().generateAndImportCards(notes,
        mediaList: allImagePaths, internalMediaTypes: ["choice_card"]);
  }

  /// 处理选择题跨文件模式
  Future<void> _handleChoiceCrossFileMode() async {
    if (qFile.isEmpty || aFile.isEmpty) {
      progressController.updateProgress(
        status: "error",
        message: 'anki.word_card.please_select_question_answer_files'.tr,
      );
      return;
    }

    final questionPattern = _validateFieldPattern(
        'Question', 'anki.word_card.please_set_question_field_pattern'.tr);
    final answerPattern = _validateFieldPattern(
        'Answers', 'anki.word_card.please_set_answers_field_pattern'.tr);
    final tempDir = await PathUtils.tempDir;
    final webviewController = Get.find<WebviewController>();
    final allImagePaths = <String>[];
    // 处理问题文件
    final rawHtml = await AnkiConnectController().docx2html(qFile.value);
    final (html, imagePaths) =
        await replaceHtmlBase64Img(rawHtml, tempDir, htmlType: "document");
    allImagePaths.addAll(imagePaths);
    final text = await webviewController.htmlToText(html);
    final qTempFilePath = await PathUtils.getTempFilePath(".txt");
    File(qTempFilePath).writeAsStringSync(text);

    final textMapQuestion = await AnkiConnectController().splitText(
      qTempFilePath,
      questionPattern,
      parentDeck.value,
      isCreateSubDeck.value ? subDeckPrefix.value : null,
    );
    // 处理答案文件
    final rawHtmlAnswer = await AnkiConnectController().docx2html(aFile.value);
    final (htmlAnswer, imagePathsAnswer) = await replaceHtmlBase64Img(
        rawHtmlAnswer, tempDir,
        htmlType: "document");
    allImagePaths.addAll(imagePathsAnswer);
    final textAnswer = await webviewController.htmlToText(htmlAnswer);
    final aTempFilePath = await PathUtils.getTempFilePath(".txt");
    File(aTempFilePath).writeAsStringSync(textAnswer);
    final textMapAnswer = await AnkiConnectController().splitText(
      aTempFilePath,
      answerPattern,
      parentDeck.value,
      isCreateSubDeck.value ? subDeckPrefix.value : null,
    );
    progressController.updateProgress(
      status: "running",
      message: 'anki.word_card.extract_card'.tr,
      current: 30,
      total: 100,
    );
    final notes = <AnkiNote>[];
    final totalQuestions =
        textMapQuestion.values.fold<int>(0, (sum, list) => sum + list.length);
    var currentQuestion = 0;

    for (var entry in textMapQuestion.entries) {
      final deckName = entry.key;
      final questionTexts = entry.value;
      final answerTexts = textMapAnswer[deckName] ?? [];

      if (questionTexts.length != answerTexts.length) {
        progressController.updateProgress(
          status: "error",
          message: 'anki.word_card.question_answer_count_mismatch'.trParams({
            'deck': deckName,
            'questionCount': questionTexts.length.toString(),
            'answerCount': answerTexts.length.toString()
          }),
        );
        return;
      }

      for (var i = 0; i < questionTexts.length; i++) {
        final qText = questionTexts[i];
        final aText = answerTexts[i];
        logger.i("qText: $qText");
        logger.i("aText: $aText");
        final extractedContent = <String, String>{};
        final qMatches = <PatternMatch>[];
        final aMatches = <PatternMatch>[];

        for (final mapping in fieldMappings.entries) {
          final field = mapping.key;
          final pattern = mapping.value;
          if (pattern.regex.isEmpty) continue;

          final regExp = RegExp(pattern.regex, multiLine: true);
          if (field == "Options") {
            final optionMatches = regExp.allMatches(qText).toList();

            // 找出Answers和Remarks的位置，用于过滤选项
            int answersPosition = qText.length;
            int remarksPosition = qText.length;

            if (fieldMappings.containsKey("Answers") &&
                fieldMappings["Answers"]!.regex.isNotEmpty) {
              final answersRegExp =
                  RegExp(fieldMappings["Answers"]!.regex, multiLine: true);
              final answersMatch = answersRegExp.firstMatch(qText);
              if (answersMatch != null) {
                answersPosition = answersMatch.start;
              }
            }

            if (fieldMappings.containsKey("Remarks") &&
                fieldMappings["Remarks"]!.regex.isNotEmpty) {
              final remarksRegExp =
                  RegExp(fieldMappings["Remarks"]!.regex, multiLine: true);
              final remarksMatch = remarksRegExp.firstMatch(qText);
              if (remarksMatch != null) {
                remarksPosition = remarksMatch.start;
              }
            }

            // 只添加位于答案和解析之前的选项匹配
            for (var optionMatch in optionMatches) {
              if (optionMatch.start < answersPosition &&
                  optionMatch.start < remarksPosition) {
                qMatches.add(
                    PatternMatch(field, pattern.regex, optionMatch, false));
              }
            }
            continue;
          }

          final qMatch = regExp.firstMatch(qText);
          final aMatch = regExp.firstMatch(aText);

          if (qMatch != null) {
            qMatches.add(
                PatternMatch(field, pattern.regex, qMatch, pattern.keepPrefix));
          }
          if (aMatch != null) {
            aMatches.add(
                PatternMatch(field, pattern.regex, aMatch, pattern.keepPrefix));
          }
        }

        final qContent = _extractContent(qText, qMatches);
        final aContent = _extractContent(aText, aMatches);
        for (var entry in qContent.entries) {
          extractedContent[entry.key] = entry.value;
        }
        for (var entry in aContent.entries) {
          if (!extractedContent.containsKey(entry.key)) {
            extractedContent[entry.key] = entry.value;
          }
        }

        // notes.add(_createNote(deckName, extractedContent, cardModel.value));
        final note = _createNote(deckName, extractedContent, cardModel.value);
        notes.add(note);
        currentQuestion++;
        progressController.updateProgress(
          status: "running",
          message: 'anki.word_card.processing_question'
              .trParams({'current': currentQuestion.toString()}),
          current: currentQuestion.toDouble(),
          total: totalQuestions.toDouble(),
        );
      }
    }
    await AnkiConnectController().generateAndImportCards(notes,
        mediaList: allImagePaths, internalMediaTypes: ["choice_card"]);
  }

  /// 处理判断题同文件模式
  Future<void> _handleJudgeSameFileMode() async {
    _validateFileSelection();
    final questionPattern = _validateFieldPattern(
        'Question', 'anki.word_card.please_set_question_field_pattern'.tr);

    final notes = <AnkiNote>[];
    final webviewController = Get.find<WebviewController>();
    Map<String, String> fieldPatterns = {};
    for (final mapping in fieldMappings.entries) {
      fieldPatterns[mapping.key] = mapping.value.regex;
    }
    final tempDir = await PathUtils.tempDir;
    final allImagePaths = <String>[];

    for (final filePath in selectedFilePaths) {
      final rawHtml = await AnkiConnectController().docx2html(filePath);
      final (html, imagePaths) =
          await replaceHtmlBase64Img(rawHtml, tempDir, htmlType: "document");
      allImagePaths.addAll(imagePaths);
      final text = await webviewController.htmlToText(html);
      // 保存到临时文件
      final tempFilePath = await PathUtils.getTempFilePath(".txt");
      File(tempFilePath).writeAsStringSync(text);
      final textMap = await AnkiConnectController().splitText(
        tempFilePath,
        questionPattern,
        parentDeck.value,
        isCreateSubDeck.value ? subDeckPrefix.value : null,
      );
      final totalQuestions =
          textMap.values.fold<int>(0, (sum, list) => sum + list.length);
      var currentQuestion = 0;
      progressController.updateProgress(
        status: "running",
        message: 'anki.word_card.extract_card'.tr,
        current: 30,
        total: 100,
      );
      for (var entry in textMap.entries) {
        final deckName = entry.key;
        final texts = entry.value;

        for (var text in texts) {
          if (text.isEmpty) {
            continue;
          }
          final matches = <PatternMatch>[];
          for (final mapping in fieldMappings.entries) {
            final field = mapping.key;
            final pattern = mapping.value;
            if (pattern.regex.isEmpty) continue;

            final regExp = RegExp(pattern.regex, multiLine: true);
            final match = regExp.firstMatch(text);
            if (match != null) {
              matches.add(PatternMatch(
                  field, pattern.regex, match, pattern.keepPrefix));
            }
          }
          final extractedContent = _extractContent(text, matches);
          // 设置固定的Options字段
          extractedContent['Options'] =
              'anki.word_card.fixed_options_true_false'.tr;
          final note = _createNote(deckName, extractedContent, cardModel.value);
          notes.add(note);
          currentQuestion++;
          progressController.updateProgress(
            status: "running",
            message: 'anki.word_card.processing_question'
                .trParams({'current': currentQuestion.toString()}),
            current: currentQuestion.toDouble(),
            total: totalQuestions.toDouble(),
          );
        }
      }
    }
    await AnkiConnectController().generateAndImportCards(notes,
        mediaList: allImagePaths, internalMediaTypes: ["choice_card"]);
  }

  /// 处理判断题跨文件模式
  Future<void> _handleJudgeCrossFileMode() async {
    if (qFile.isEmpty || aFile.isEmpty) {
      progressController.updateProgress(
        status: "error",
        message: 'anki.word_card.please_select_question_answer_files'.tr,
      );
      return;
    }

    final questionPattern = _validateFieldPattern(
        'Question', 'anki.word_card.please_set_question_field_pattern'.tr);
    final answerPattern = _validateFieldPattern(
        'Answers', 'anki.word_card.please_set_answers_field_pattern'.tr);

    final tempDir = await PathUtils.tempDir;
    final webviewController = Get.find<WebviewController>();
    final allImagePaths = <String>[];
    // 处理问题文件
    final rawHtml = await AnkiConnectController().docx2html(qFile.value);
    final (html, imagePaths) =
        await replaceHtmlBase64Img(rawHtml, tempDir, htmlType: "document");
    allImagePaths.addAll(imagePaths);
    final text = await webviewController.htmlToText(html);
    final qTempFilePath = await PathUtils.getTempFilePath(".txt");
    File(qTempFilePath).writeAsStringSync(text);

    final textMapQuestion = await AnkiConnectController().splitText(
      qTempFilePath,
      questionPattern,
      parentDeck.value,
      isCreateSubDeck.value ? subDeckPrefix.value : null,
    );
    // 处理答案文件
    final rawHtmlAnswer = await AnkiConnectController().docx2html(aFile.value);
    final (htmlAnswer, imagePathsAnswer) = await replaceHtmlBase64Img(
        rawHtmlAnswer, tempDir,
        htmlType: "document");
    allImagePaths.addAll(imagePathsAnswer);
    final textAnswer = await webviewController.htmlToText(htmlAnswer);
    final aTempFilePath = await PathUtils.getTempFilePath(".txt");
    File(aTempFilePath).writeAsStringSync(textAnswer);
    final textMapAnswer = await AnkiConnectController().splitText(
      aTempFilePath,
      answerPattern,
      parentDeck.value,
      isCreateSubDeck.value ? subDeckPrefix.value : null,
    );
    final notes = <AnkiNote>[];
    final totalQuestions =
        textMapQuestion.values.fold<int>(0, (sum, list) => sum + list.length);
    var currentQuestion = 0;
    progressController.updateProgress(
      status: "running",
      message: 'anki.word_card.extract_card'.tr,
      current: 30,
      total: 100,
    );
    for (var entry in textMapQuestion.entries) {
      final deckName = entry.key;
      final questionTexts = entry.value;
      final answerTexts = textMapAnswer[deckName] ?? [];

      if (questionTexts.length != answerTexts.length) {
        progressController.updateProgress(
          status: "error",
          message: 'anki.word_card.question_answer_count_mismatch'.trParams({
            'deck': deckName,
            'questionCount': questionTexts.length.toString(),
            'answerCount': answerTexts.length.toString()
          }),
        );
        return;
      }

      for (var i = 0; i < questionTexts.length; i++) {
        final qText = questionTexts[i];
        final aText = answerTexts[i];
        if (qText.isEmpty && aText.isEmpty) {
          continue;
        }
        final extractedContent = <String, String>{};
        final qMatches = <PatternMatch>[];
        final aMatches = <PatternMatch>[];

        for (final mapping in fieldMappings.entries) {
          final field = mapping.key;
          final pattern = mapping.value;
          if (pattern.regex.isEmpty) continue;

          final regExp = RegExp(pattern.regex, multiLine: true);
          final qMatch = regExp.firstMatch(qText);
          final aMatch = regExp.firstMatch(aText);

          if (qMatch != null) {
            qMatches.add(
                PatternMatch(field, pattern.regex, qMatch, pattern.keepPrefix));
          }
          if (aMatch != null) {
            aMatches.add(
                PatternMatch(field, pattern.regex, aMatch, pattern.keepPrefix));
          }
        }

        final qContent = _extractContent(qText, qMatches);
        final aContent = _extractContent(aText, aMatches);
        for (var entry in qContent.entries) {
          extractedContent[entry.key] = entry.value;
        }
        for (var entry in aContent.entries) {
          if (!extractedContent.containsKey(entry.key)) {
            extractedContent[entry.key] = entry.value;
          }
        }
        // 设置固定的Options字段
        extractedContent['Options'] =
            'anki.word_card.fixed_options_true_false'.tr;
        final note = _createNote(deckName, extractedContent, cardModel.value);
        notes.add(note);
        currentQuestion++;
        progressController.updateProgress(
          status: "running",
          message: 'anki.word_card.processing_question'
              .trParams({'current': currentQuestion.toString()}),
          current: currentQuestion.toDouble(),
          total: totalQuestions.toDouble(),
        );
      }
    }

    await AnkiConnectController().generateAndImportCards(notes,
        mediaList: allImagePaths, internalMediaTypes: ["choice_card"]);
  }

  void _initProgressController(BuildContext context) {
    progressController.reset(
      showOutputHint: settingController.getExportCardMode() == "apkg",
      numberButtons: settingController.getExportCardMode() == "apkg" ? 2 : 0,
    );
    progressController.showProgressDialog(context);
  }
}
