import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:excel/excel.dart';
import 'dart:convert';
import 'package:file_picker/file_picker.dart';

class ExcelCardPageController extends GetxController {
  // 已有数据
  final cardModel = "".obs;
  final tabController = ShadTabsController(value: 'qa');
  final sheetData = <String, List<dynamic>>{}.obs;
  final sheetList = <Map<String, String>>[].obs;
  final colList = <Map<String, String>>[].obs;

  final answerPositionList = <Map<String, String>>[
    {
      "label": "anki.excel_card.answer_in_column".tr,
      "value": "answer_in_column"
    },
    {"label": "anki.excel_card.answer_in_text".tr, "value": "answer_in_text"},
  ].obs;

  final answerPatternList = <Map<String, String>>[
    {"label": "([A-G]+)", "value": "[（(]\\s*([A-G\\s]+)\\s*[)）]"},
    {"label": "anki.excel_card.free_match".tr, "value": "([A-G\\s]+)"},
  ].obs;

  final optionPositionList = <Map<String, String>>[
    {
      "label": "anki.excel_card.option_in_column".tr,
      "value": "option_in_column"
    },
    {"label": "anki.excel_card.option_in_text".tr, "value": "option_in_text"},
  ].obs;

  final optionPatternList = <Map<String, String>>[
    {"label": "[A-G][.．、：:]", "value": "[A-G]\\s*[.．、：:]"},
  ].obs;
  final correctPatternList = <Map<String, String>>[
    {"label": "√|Y|是|对|正确|T|True|Yes", "value": "(√|Y|是|对|正确|T|TRUE|YES)"},
    {
      "label": "(√|Y|是|对|正确|T|True|Yes)",
      "value": "[(（]\\s*(√|Y|是|对|正确|T|TRUE|YES)\\s*[)）]"
    },
    {
      "label": "答案: √|Y|是|对|正确|T|True|Yes",
      "value": "答案[:：]\\s*(√|Y|是|对|正确|T|TRUE|YES)\\s*"
    },
    {"label": "(√)", "value": "[(（]\\s*√\\s*[)）]"},
    {"label": "(Y)", "value": "[(（]\\s*Y\\s*[)）]"},
    {"label": "(是)", "value": "[(（]\\s*是\\s*[)）]"},
    {"label": "(对)", "value": "[(（]\\s*对\\s*[)）]"},
    {"label": "(正确)", "value": "[(（]\\s*正确\\s*[)）]"},
  ].obs;
  final wrongPatternList = <Map<String, String>>[
    {"label": "x|N|否|错|错误|F|False|No", "value": "(X|×|N|否|错|错误|F|FALSE|NO)"},
    {
      "label": "(x|N|否|错|错误|F|False|No)",
      "value": "[(（]\\s*(X|×|N|否|错|错误|F|FALSE|NO)\\s*[)）]"
    },
    {
      "label": "答案: x|N|否|错|错误|F|False|No",
      "value": "答案[:：]\\s*(X|×|N|否|错|错误|F|FALSE|NO)\\s*"
    },
    {"label": "(x)", "value": "[(（]\\s*[X×]\\s*[)）]"},
    {"label": "(否)", "value": "[(（]\\s*否\\s*[)）]"},
    {"label": "(错)", "value": "[(（]\\s*错\\s*[)）]"},
    {"label": "(错误)", "value": "[(（]\\s*错误\\s*[)）]"},
  ].obs;
  // 表单参数
  final input = ''.obs;
  final sheet = ''.obs;
  final parentDeck = ''.obs;
  final isCreateSubDeck = false.obs;
  final subDeckCols = <String>[].obs;
  final tagCols = <String>[].obs;
  final guidCol = ''.obs;
  final tags = <String>[].obs;
  final answerPosition = 'answer_in_column'.obs;
  final answerPattern = ''.obs;
  final optionPosition = 'option_in_column'.obs;
  final optionPattern = ''.obs;

  final judgeAnswerPosition = 'answer_in_column'.obs;
  final correctPattern = ''.obs;
  final wrongPattern = ''.obs;

  // 错误
  final inputError = ''.obs;
  // 控制器
  final ankiConnectController = Get.find<AnkiConnectController>();
  final progressController = Get.find<ProgressController>();
  final messageController = Get.find<MessageController>();
  final settingController = Get.find<SettingController>();
  // 添加字段映射相关变量
  final fieldMappings = <String, List<String>>{}.obs;

  @override
  void onInit() async {
    super.onInit();

    // Check if AnkiConnectController data is loaded, if not, load it
    if (ankiConnectController.modelList.isEmpty ||
        ankiConnectController.parentDeckList.isEmpty) {
      await ankiConnectController.resetAnkiConnectData();
    }
        if (ankiConnectController.parentDeckList.isNotEmpty) {
      parentDeck.value = ankiConnectController.parentDeckList[0];
    }

    // Now set initial values with null checks
    if (ankiConnectController.modelList.isNotEmpty) {
      cardModel.value = "Kevin Text QA Card v2";
      await updateFieldList(cardModel.value);
    }
    if (ankiConnectController.parentDeckList.isNotEmpty) {
      parentDeck.value = ankiConnectController.parentDeckList[0];
    }

    if (answerPatternList.isNotEmpty) {
      answerPattern.value = answerPatternList[0]['value']!;
    }
    if (optionPatternList.isNotEmpty) {
      optionPattern.value = optionPatternList[0]['value']!;
    }
    if (correctPatternList.isNotEmpty) {
      correctPattern.value = correctPatternList[0]['value']!;
    }
    if (wrongPatternList.isNotEmpty) {
      wrongPattern.value = wrongPatternList[0]['value']!;
    }
  }

  @override
  void onClose() {
    super.onClose();
  }

  // 初始化字段映射
  void initFieldMappings() {
    fieldMappings.clear();
    for (var field in ankiConnectController.fieldList) {
      fieldMappings[field] = <String>[]; // 其他字段也初始化为空列表
    }
    fieldMappings.refresh();
    update();
  }

  // 更新字段映射
  void updateFieldMapping(String field, List<String>? value) {
    fieldMappings[field] = value ?? [];
    fieldMappings.refresh();
    update();
  }

  // 获取字段映射值
  List<String> getFieldMappingValue(String field) {
    return (fieldMappings[field] ?? <String>[]);
  }

  Future<void> updateFieldList(String modelName) async {
    try {
      await ankiConnectController.updateFieldList(modelName);
    } catch (e) {
      logger.e("updateFieldList error: $e");
    }
    initFieldMappings();
    fieldMappings.refresh();
    colList.refresh();
    update();
  }

  Future<void> updateSheetData(String sheetName) async {
    sheet.value = sheetName;
    colList.value = List<Map<String, String>>.from(
        sheetData[sheetName]!.asMap().entries.map((entry) => <String, String>{
              "label": entry.value.toString(),
              "value": entry.key.toString(),
            }));
    colList.refresh();
    update();
  }

  // 获取列标签
  String getColumnLabel(String value) {
    try {
      final idx = int.tryParse(value);
      if (idx == null || idx >= colList.length) return '';
      return colList[idx]['label'] ?? '';
    } catch (e) {
      return '';
    }
  }

  Future<String> validateInput(String path) async {
    try {
      final data = {
        "file_path": path,
        "show_progress": false,
      };
      final resp = await messageController.request(data, "excel/validate");
      logger.i("resp: ${resp.status}");
      if (resp.status == 'success') {
        sheetData.value =
            Map<String, List<dynamic>>.from(jsonDecode(resp.data));
        logger.i("sheetData: ${sheetData.value}");
        // 转换数据格式为UI所需的格式
        sheetList.value = sheetData.keys
            .map((name) => <String, String>{
                  "label": name,
                  "value": name,
                })
            .toList();

        // 找到第一个有非空列的sheet并设置默认值
        bool foundValidSheet = false;
        for (final sheetName in sheetData.keys) {
          final sheetColumns = sheetData[sheetName]!;
          if (sheetColumns.isNotEmpty) {
            sheet.value = sheetName;
            colList.value = List<Map<String, String>>.from(
                sheetColumns.asMap().entries.map((entry) => <String, String>{
                      "label": entry.value.toString(),
                      "value": entry.key.toString(),
                    }));
            foundValidSheet = true;
            break;
          }
        }

        // 如果没有找到有效的sheet，则使用第一个sheet（即使它可能为空）
        if (!foundValidSheet && sheetList.isNotEmpty) {
          sheet.value = sheetList.first['value']!;
          final firstSheet = sheetData.keys.first;
          colList.value = List<Map<String, String>>.from(sheetData[firstSheet]!
              .asMap()
              .entries
              .map((entry) => <String, String>{
                    "label": entry.value.toString(),
                    "value": entry.key.toString(),
                  }));
        }

        if (colList.isEmpty) {
          return "anki.excel_card.no_column_info".tr;
        }
        return "";
      } else {
        return resp.message;
      }
    } catch (e) {
      logger.e("validateInput error: $e");
      return "anki.excel_card.read_excel_failed"
          .trParams({'error': e.toString()});
    }
  }

  Future<void> selectFile() async {
    logger.i("selectFile");
    final result = await FilePicker.platform.pickFiles(
        dialogTitle: "anki.excel_card.select_file_dialog".tr,
        type: FileType.custom,
        allowedExtensions: ["xlsx"],
        allowMultiple: false,
        compressionQuality: 0);
    if (result != null) {
      input.value = result.files.single.path!;
      logger.i("input: ${input.value}");
      validateInput(input.value);
      logger.i("colList: ${colList.value}");
    }
  }

  Future<void> submit(BuildContext context) async {
    logger.i("submit");
    if (input.value.isEmpty) {
      showToastNotification(
          context, "anki.excel_card.select_excel_file_error".tr, "",
          type: "error");
      return;
    }
    final exportCardMode = settingController.getExportCardMode();
    final address = settingController.ankiConnectUrl;
    progressController.reset(
      showOutputHint: exportCardMode == "apkg",
      numberButtons: exportCardMode == "apkg" ? 2 : 0,
    );
    progressController.showProgressDialog(context);
    try {
      final outputPath = await PathUtils.getOutputApkgPath();
      final data = {
        'file_path': input.value,
        'export_card_mode': settingController.cardMode.value,
        'sheet': sheet.value,
        'address': address.value,
        'parent_deck': parentDeck.value,
        'card_model': cardModel.value,
        'output_path': outputPath,
        'sub_deck_cols': subDeckCols.value,
        'tag_cols': tagCols.value,
        'guid_col': guidCol.value,
        'tags': tags.value,
        'field_mappings': fieldMappings.value,
        'fields': ankiConnectController.fieldList.value,
        'op_type': tabController.selected, // 'qa', 'choice', 'judge'
        'answer_position': answerPosition.value,
        'answer_pattern': answerPattern.value,
        'option_position': optionPosition.value,
        'option_pattern': optionPattern.value,
        'judge_answer_position': judgeAnswerPosition.value,
        'correct_pattern': correctPattern.value,
        'wrong_pattern': wrongPattern.value,
        'show_progress': true,
      };
      final resp = await messageController.request(data, "excel/make_card");
      logger.i("resp: $resp");
      if (resp.status == 'success') {
        if (exportCardMode == "ankiconnect") {
          await AnkiConnectController().importApkg(resp.data, isDelete: true);
        }
        progressController.outputPath.value = resp.data;
        progressController.updateProgress(
          status: "completed",
          message: 'common.completed'.tr,
          current: 100.0,
          total: 100.0,
        );
      } else {
        progressController.updateProgress(
          status: "error",
          message: resp.message,
          current: 100.0,
          total: 100.0,
        );
      }
      logger.i("resp: $resp");
    } catch (e) {
      progressController.updateProgress(
        status: "error",
        message: e.toString(),
        current: 100.0,
        total: 100.0,
      );
      logger.e("submit error: $e");
    }
  }
}
