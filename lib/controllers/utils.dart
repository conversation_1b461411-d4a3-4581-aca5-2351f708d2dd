import 'dart:io';
import 'package:anki_guru/controllers/common.dart';
import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';
import 'package:external_path/external_path.dart';
import 'package:flutter/services.dart';
import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'dart:convert';
import 'package:toastification/toastification.dart';
import 'package:flutter/material.dart' hide Element;
import 'package:markdown/markdown.dart' hide Text, Element;
import 'package:local_notifier/local_notifier.dart';
import 'package:ulid/ulid.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:docx_to_text/docx_to_text.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:syncfusion_flutter_pdf/pdf.dart';
import 'package:excel/excel.dart';
import 'package:anki_guru/src/bindings/bindings.dart';

// Method channel for macOS notification permissions
const MethodChannel _notificationPermissionChannel = MethodChannel('notification_permission');

class CustomResponse {
  final String status;
  final String message;
  final String data;
  CustomResponse(
      {required this.status, required this.message, required this.data});
}

// 定义一个匹配结果的数据类
class PatternMatch {
  final String field; // 匹配类型 (question, answer, hint 等)
  final String regex; // 匹配正则
  final Match? match; // 匹配结果
  final bool keepPrefix; // 是否保留前缀
  PatternMatch(this.field, this.regex, this.match, this.keepPrefix);

  @override
  String toString() {
    return "PatternMatch(field: $field, regex: $regex, match: ${match?.group(0)}, keepPrefix: $keepPrefix)";
  }
}

// 添加firstWhereOrNull扩展方法
extension IterableExtension<T> on Iterable<T> {
  T? firstWhereOrNull(bool Function(T) test) {
    for (var element in this) {
      if (test(element)) return element;
    }
    return null;
  }
}

bool _isNeedUpdate(String currentVersion, String latestVersion) {
  List<int> current = currentVersion.split('.').map(int.parse).toList();
  List<int> latest = latestVersion.split('.').map(int.parse).toList();

  // 确保两个列表长度相同
  while (current.length < latest.length) {
    current.add(0);
  }
  while (latest.length < current.length) {
    latest.add(0);
  }

  // 从高位到低位比较版本号
  for (int i = 0; i < current.length; i++) {
    if (latest[i] > current[i]) return true;
    if (latest[i] < current[i]) return false;
  }
  return false; // 版本号完全相同
}

Future<void> checkUpdate(BuildContext context,
    {bool isBackground = false}) async {
  final settingController = Get.find<SettingController>();
  final dio = Dio(BaseOptions(
    baseUrl: 'https://anki.kevin2li.top',
    connectTimeout: const Duration(seconds: 5),
  ));
  try {
    final response = await dio.get('/check_update/mobile');
    final data = response.data;
    logger.i(data);
    if (data['status'] == 'success') {
      final obj = jsonDecode(data['data']); // 如果data['data']是字符串需要二次解析
      final latestVersion = obj['version'];
      final downloadUrl = obj['url'];
      if (_isNeedUpdate(settingController.version.value, latestVersion)) {
        // 有新版本，显示更新对话框
        if (context.mounted) {
          if (!isBackground) {
            showDialog(
              context: context,
              barrierDismissible: false,
              builder: (context) => AlertDialog(
                title: const Text('发现新版本'),
                content: Text(
                    '当前版本: ${settingController.version}\n最新版本: $latestVersion'),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('暂不更新'),
                  ),
                  FilledButton(
                    onPressed: () {
                      Navigator.pop(context);
                      launchUrl(Uri.parse(downloadUrl));
                    },
                    child: const Text('立即更新'),
                  ),
                ],
              ),
            );
          } else {
            showToastNotification(context, "发现新版本：$latestVersion", '',
                type: "success");
          }
        }
      } else {
        // 已是最新版本
        if (!isBackground && context.mounted) {
          showToastNotification(context, "已是最新版本", '', type: "success");
        }
      }
    } else {
      logger.e('Update check failed: ${data['message']}');
    }
  } on DioException catch (e) {
    logger.e('Update check network error: ${e.message}');
    if (!isBackground && context.mounted) {
      showToastNotification(context, "更新检查失败", "网络连接异常: ${e.message}",
          type: "error");
    }
  } catch (e, stack) {
    logger.e('Update check error: $e\n$stack');
    if (!isBackground && context.mounted) {
      showToastNotification(context, "更新检查异常", "请检查网络后重试", type: "error");
    }
  }
}

Future<void> openFileBySystemDefault(String path) async {
  // option1:
  // final command = Platform.isWindows
  //     ? 'start "" "$path"'
  //     : Platform.isMacOS
  //         ? 'open "$path"'
  //         : 'xdg-open "$path"';
  // try {
  //   await Process.run(
  //     Platform.isWindows ? 'cmd' : '/bin/sh',
  //     Platform.isWindows ? ['/c', command] : ['-c', command],
  //     runInShell: true,
  //   );
  //   logger.i('Opened PDF with default program: $path');
  // } catch (e, stackTrace) {
  //   logger.e('Failed to open PDF: $e\n$stackTrace');
  // }

  // option2:
  final data = {
    "path": path,
    "show_progress": false,
  };
  final messageController = Get.find<MessageController>();
  final resp = await messageController.request(data, "open_file");
  logger.d(resp);
}

void showToastNotification(
    BuildContext? context, String title, String description,
    {String type = "success"}) {
  var type0 = ToastificationType.info;
  if (type == "success") {
    type0 = ToastificationType.success;
  } else if (type == "error") {
    type0 = ToastificationType.error;
  } else if (type == "warning") {
    type0 = ToastificationType.warning;
  } else if (type == "info") {
    type0 = ToastificationType.info;
  }
  toastification.show(
      context: context,
      alignment: Alignment.topCenter,
      title: Text(title),
      description: description.isEmpty ? null : Text(description),
      type: type0,
      style: ToastificationStyle.flat,
      showProgressBar: false,
      autoCloseDuration: const Duration(seconds: 2));
}

// 删除注释
Future<void> deleteAnnotations(String filePath, String outputPath,
    String pageRange, List<String> annotationIds) async {
  final PdfDocument document = PdfDocument(
    inputBytes: File(filePath).readAsBytesSync(),
  );
  final List<int> pages = parsePageRange(pageRange, document.pages.count);

  try {
    for (int i in pages) {
      try {
        PdfPage page = document.pages[i];
        PdfAnnotationCollection annotationCollection = page.annotations;

        // Always process from last to first to avoid index shifting issues
        for (int j = annotationCollection.count - 1; j >= 0; j--) {
          try {
            final annotation = annotationCollection[j];
            final rect = annotation.bounds;
            final pageHeight = page.size.height;
            final id =
                "${i + 1}-[${rect.left.toStringAsFixed(3)}, ${(pageHeight - rect.bottom).toStringAsFixed(3)}, ${rect.right.toStringAsFixed(3)}, ${(pageHeight - rect.top).toStringAsFixed(3)}]";
            if (annotationIds.isEmpty || annotationIds.contains(id)) {
              annotationCollection.remove(annotation);
            }
          } catch (e, stack) {
            logger.e("Error removing annotation at index $j: $e\n$stack");
            continue;
          }
        }
      } catch (e, stack) {
        logger.e("Error processing annotations on page $i: $e\n$stack");
        // 跳过本页，继续处理其他页面
        continue;
      }
    }

    // Save the document only once after all processing is complete
    File(outputPath).writeAsBytesSync(
      await document.save(),
    );
  } catch (e) {
    logger.e("Error during annotation removal: $e");
    rethrow; // Re-throw to allow caller to handle the error
  } finally {
    // Always dispose the document to prevent memory leaks
    document.dispose();
  }
}

/// 显示系统通知
/// 使用local_notifier包在桌面平台显示原生系统通知
/// 在移动平台使用fallback机制
/// macOS平台包含权限检查和请求处理
void showLocalNotification(String title, String description) async {
  try {
    if (Platform.isWindows || Platform.isMacOS || Platform.isLinux) {
      final timestamp = DateTime.now().toIso8601String();

      // macOS特殊处理：检查和请求通知权限
      if (Platform.isMacOS) {
        final hasPermission = await _checkAndRequestMacOSNotificationPermission();
        if (!hasPermission) {
          logger.w('[$timestamp] macOS通知权限未授予，使用fallback通知');
          _showMobileFallbackNotification(title, description);
          return;
        }
      }

      // 桌面平台使用local_notifier显示原生系统通知
      final notification = LocalNotification(
        title: title,
        body: description,
        silent: false,
      );

      // 显示通知
      await notification.show();

      // 记录通知显示日志
      logger.d('[$timestamp] 系统通知已显示: $title - $description');
    } else {
      // 移动平台fallback - 使用应用内通知
      _showMobileFallbackNotification(title, description);
    }
  } catch (e) {
    final timestamp = DateTime.now().toIso8601String();
    logger.e('[$timestamp] 显示系统通知失败: $e');

    // 如果系统通知失败，使用fallback
    _showMobileFallbackNotification(title, description);
  }
}

/// 检查和请求macOS通知权限
/// 使用原生方法通道直接请求系统权限
/// 返回true如果权限已授予或成功获取，false如果权限被拒绝
Future<bool> _checkAndRequestMacOSNotificationPermission() async {
  try {
    final timestamp = DateTime.now().toIso8601String();
    logger.d('[$timestamp] 开始检查macOS通知权限');

    // 首先检查当前权限状态
    final currentStatus = await _notificationPermissionChannel.invokeMethod('checkNotificationPermission');
    logger.d('[$timestamp] 当前通知权限状态: $currentStatus');

    final status = currentStatus['status'] as String;
    final isAuthorized = currentStatus['isAuthorized'] as bool;

    // 如果已经授权，直接返回true
    if (isAuthorized) {
      logger.d('[$timestamp] 通知权限已授予');
      return true;
    }

    // 如果权限被明确拒绝，不再请求
    if (status == 'denied') {
      logger.w('[$timestamp] 通知权限已被用户拒绝');
      _logMacOSNotificationPermissionGuidance(timestamp);
      return false;
    }

    // 如果权限未确定，请求权限
    if (status == 'notDetermined') {
      logger.i('[$timestamp] 请求通知权限');
      final requestResult = await _notificationPermissionChannel.invokeMethod('requestNotificationPermission');
      logger.d('[$timestamp] 权限请求结果: $requestResult');

      final granted = requestResult['granted'] as bool;
      final newStatus = requestResult['status'] as String;
      final newIsAuthorized = requestResult['isAuthorized'] as bool;

      if (granted && newIsAuthorized) {
        logger.i('[$timestamp] 通知权限已成功获取');
        return true;
      } else {
        logger.w('[$timestamp] 通知权限请求被拒绝，状态: $newStatus');
        _logMacOSNotificationPermissionGuidance(timestamp);
        return false;
      }
    }

    // 其他状态（provisional, ephemeral等）
    logger.w('[$timestamp] 未知的权限状态: $status');
    return false;

  } catch (e) {
    final timestamp = DateTime.now().toIso8601String();
    logger.e('[$timestamp] macOS通知权限检查失败: $e');

    // 如果方法通道调用失败，回退到原来的测试方法
    logger.w('[$timestamp] 回退到测试通知方法检查权限');
    return await _fallbackPermissionCheck();
  }
}

/// 回退权限检查方法（当原生方法通道失败时使用）
/// 使用测试通知的方式检查权限
Future<bool> _fallbackPermissionCheck() async {
  try {
    final timestamp = DateTime.now().toIso8601String();
    logger.d('[$timestamp] 使用回退方法检查macOS通知权限');

    // 尝试显示一个测试通知来检查权限
    final testNotification = LocalNotification(
      title: 'Permission Test',
      body: 'Testing notification permissions...',
      silent: true, // 静默测试，减少用户打扰
    );

    // 尝试显示测试通知
    await testNotification.show();

    // 等待短暂时间确保通知已处理
    await Future.delayed(const Duration(milliseconds: 100));

    // 尝试关闭测试通知
    try {
      await localNotifier.close(testNotification);
    } catch (closeError) {
      // 关闭失败不影响权限检查结果
      logger.d('[$timestamp] 测试通知关闭失败（正常）: $closeError');
    }

    logger.d('[$timestamp] 回退权限检查通过');
    return true;
  } catch (e) {
    final timestamp = DateTime.now().toIso8601String();
    logger.w('[$timestamp] 回退权限检查失败: $e');

    // 检查错误是否与权限相关
    final errorMessage = e.toString().toLowerCase();
    if (errorMessage.contains('permission') ||
        errorMessage.contains('authorization') ||
        errorMessage.contains('denied') ||
        errorMessage.contains('not allowed') ||
        errorMessage.contains('unauthorized') ||
        errorMessage.contains('access denied')) {
      logger.w('[$timestamp] 检测到权限相关错误，通知权限可能未授予');
      return false;
    }

    // 如果不是权限错误，可能是其他问题，仍然尝试显示通知
    logger.w('[$timestamp] 非权限相关错误，将尝试显示通知: $e');
    return true;
  }
}

/// 记录macOS通知权限设置指导信息
void _logMacOSNotificationPermissionGuidance(String timestamp) {
  logger.i('[$timestamp] macOS通知权限设置指导:');
  logger.i('[$timestamp] 1. 打开"系统偏好设置" > "通知与专注模式"');
  logger.i('[$timestamp] 2. 在左侧列表中找到"PDF Guru Anki"应用');
  logger.i('[$timestamp] 3. 确保"允许通知"选项已启用');
  logger.i('[$timestamp] 4. 重启应用以使权限设置生效');
}

/// 移动平台或系统通知失败时的fallback通知
void _showMobileFallbackNotification(String title, String description) {
  try {
    // 使用Get.snackbar作为fallback
    Get.snackbar(
      title,
      description,
      duration: const Duration(seconds: 3),
      snackPosition: SnackPosition.TOP,
      backgroundColor: Colors.black87,
      colorText: Colors.white,
      margin: const EdgeInsets.all(10),
      borderRadius: 8,
      isDismissible: true,
    );

    final timestamp = DateTime.now().toIso8601String();
    logger.d('[$timestamp] Fallback通知已显示: $title - $description');
  } catch (e) {
    final timestamp = DateTime.now().toIso8601String();
    logger.e('[$timestamp] Fallback通知显示失败: $e');
  }
}

/// 检查系统通知权限状态
/// 返回权限检查结果和相关信息
Future<Map<String, dynamic>> checkNotificationPermissionStatus() async {
  final timestamp = DateTime.now().toIso8601String();

  if (!Platform.isMacOS) {
    // 非macOS平台默认认为有权限
    return {
      'hasPermission': true,
      'platform': Platform.operatingSystem,
      'message': 'Notification permissions not required on this platform',
      'needsUserAction': false,
    };
  }

  logger.i('[$timestamp] 检查macOS通知权限状态');

  try {
    // 使用原生方法通道检查权限状态
    final currentStatus = await _notificationPermissionChannel.invokeMethod('checkNotificationPermission');
    logger.d('[$timestamp] 原生权限状态: $currentStatus');

    final status = currentStatus['status'] as String;
    final isAuthorized = currentStatus['isAuthorized'] as bool;
    final alertSetting = currentStatus['alertSetting'] as String;
    final badgeSetting = currentStatus['badgeSetting'] as String;
    final soundSetting = currentStatus['soundSetting'] as String;

    return {
      'hasPermission': isAuthorized,
      'platform': 'macOS',
      'status': status,
      'alertSetting': alertSetting,
      'badgeSetting': badgeSetting,
      'soundSetting': soundSetting,
      'message': isAuthorized
          ? 'Notification permissions are granted'
          : 'Notification permissions are not granted. Please enable notifications in System Preferences.',
      'needsUserAction': !isAuthorized && status == 'denied',
      'canRequest': status == 'notDetermined',
      'guidance': isAuthorized ? null : [
        'Open System Preferences > Notifications & Focus',
        'Find "PDF Guru Anki" in the left sidebar',
        'Enable "Allow Notifications"',
        'Restart the app for changes to take effect'
      ],
    };
  } catch (e) {
    logger.e('[$timestamp] 原生权限检查失败，使用回退方法: $e');

    // 回退到原来的检查方法
    final hasPermission = await _fallbackPermissionCheck();

    return {
      'hasPermission': hasPermission,
      'platform': 'macOS',
      'status': 'unknown',
      'message': hasPermission
          ? 'Notification permissions appear to be working (fallback check)'
          : 'Notification permissions may not be granted (fallback check)',
      'needsUserAction': !hasPermission,
      'canRequest': false,
      'guidance': hasPermission ? null : [
        'Open System Preferences > Notifications & Focus',
        'Find "PDF Guru Anki" in the left sidebar',
        'Enable "Allow Notifications"',
        'Restart the app for changes to take effect'
      ],
    };
  }
}

/// 显式请求macOS通知权限
/// 返回权限请求结果
Future<Map<String, dynamic>> requestNotificationPermission() async {
  final timestamp = DateTime.now().toIso8601String();

  if (!Platform.isMacOS) {
    return {
      'success': true,
      'platform': Platform.operatingSystem,
      'message': 'Notification permissions not required on this platform',
    };
  }

  logger.i('[$timestamp] 显式请求macOS通知权限');

  try {
    // 使用原生方法通道请求权限
    final requestResult = await _notificationPermissionChannel.invokeMethod('requestNotificationPermission');
    logger.d('[$timestamp] 权限请求结果: $requestResult');

    final granted = requestResult['granted'] as bool;
    final status = requestResult['status'] as String;
    final isAuthorized = requestResult['isAuthorized'] as bool;

    return {
      'success': granted && isAuthorized,
      'platform': 'macOS',
      'granted': granted,
      'status': status,
      'isAuthorized': isAuthorized,
      'message': granted && isAuthorized
          ? 'Notification permissions granted successfully'
          : 'Notification permissions were denied or not fully granted',
    };
  } catch (e) {
    logger.e('[$timestamp] 权限请求失败: $e');

    return {
      'success': false,
      'platform': 'macOS',
      'error': e.toString(),
      'message': 'Failed to request notification permissions: ${e.toString()}',
    };
  }
}

/// 测试系统通知功能
/// 用于验证local_notifier是否正常工作，包括macOS权限检查
Future<bool> testNotificationSystem() async {
  try {
    final timestamp = DateTime.now().toIso8601String();
    logger.i('[$timestamp] 开始测试系统通知功能');

    // 在macOS上先检查权限
    if (Platform.isMacOS) {
      final hasPermission = await _checkAndRequestMacOSNotificationPermission();
      if (!hasPermission) {
        logger.w('[$timestamp] macOS通知权限未授予，测试将使用fallback通知');

        // 使用fallback通知进行测试
        _showMobileFallbackNotification('通知测试', '权限未授予，使用应用内通知进行测试');
        await Future.delayed(const Duration(seconds: 1));
        _showMobileFallbackNotification('Guru Connector', '通知测试完成（使用应用内通知）');

        return false; // 返回false表示系统通知不可用
      }
    }

    // 测试基本通知
    showLocalNotification('通知测试', '这是一个测试通知，用于验证系统通知功能是否正常工作');

    // 等待一秒后测试第二个通知
    await Future.delayed(const Duration(seconds: 1));

    showLocalNotification('Guru Connector', '系统通知功能测试完成！如果您看到这条消息，说明通知系统工作正常。');

    logger.i('[$timestamp] 系统通知测试完成');
    return true;
  } catch (e) {
    final timestamp = DateTime.now().toIso8601String();
    logger.e('[$timestamp] 系统通知测试失败: $e');
    return false;
  }
}

/// 测试WebSocket服务器连接
/// 用于调试浏览器扩展连接问题
Future<bool> testWebSocketConnection() async {
  try {
    final timestamp = DateTime.now().toIso8601String();
    logger.i('[$timestamp] 开始测试WebSocket连接');

    // 获取服务器状态
    final serverRunning = await isWebSocketServerRunning();
    final serverStatus = getWebSocketServerStatus();

    logger.i('[$timestamp] WebSocket服务器运行状态: $serverRunning');
    logger.i('[$timestamp] 服务器详细状态: $serverStatus');

    // 显示测试结果
    showLocalNotification('WebSocket连接测试',
        '服务器运行: $serverRunning, 端口: ${serverStatus['server_port']}');

    return serverRunning;
  } catch (e) {
    final timestamp = DateTime.now().toIso8601String();
    logger.e('[$timestamp] WebSocket连接测试失败: $e');

    showLocalNotification('WebSocket测试失败', '测试过程中发生错误: $e');

    return false;
  }
}

/// 将文件转换为base64字符串
Future<String> fileToBase64(String filePath) async {
  try {
    File file = File(filePath);
    List<int> fileBytes = await file.readAsBytes();
    String base64String = base64Encode(fileBytes);
    return base64String;
  } catch (e) {
    logger.e('Failed to convert file to base64: $e');
    return '';
  }
}

/// 验证页码范围字符串格式是否正确
bool validatePageRange(String range) {
  if (range.isEmpty) return true;

  // 分割逗号分隔的部分
  final parts = range.split(',');
  for (String part in parts) {
    part = part.trim();

    // 检查是否包含非法字符
    if (!RegExp(r'^[0-9N\-]+$').hasMatch(part)) {
      return false;
    }

    if (part.contains('-')) {
      // 检查范围格式
      final rangeParts = part.split('-');
      if (rangeParts.length != 2) return false;

      // 检查每个数字是否合法
      for (final num in rangeParts) {
        if (num != 'N' && !RegExp(r'^\d+$').hasMatch(num)) {
          return false;
        }
      }
    } else {
      // 检查单个数字格式
      if (part != 'N' && !RegExp(r'^\d+$').hasMatch(part)) {
        return false;
      }
    }
  }

  return true;
}

/// 解析页码范围字符串
/// 支持以下格式:
/// - 单页: "1,2,3"
/// - 范围: "1-3,5-7"
/// - 倒序范围: "3-1,7-5"
/// - N表示总页数: "1-N", "N-1", "N"
///

List<int> parsePageRange(String range, int maxPages) {
  if (range.isEmpty) {
    return List.generate(maxPages, (index) => index);
  }
  final Set<int> pages = {};
  // 替换N为实际页数
  final String normalizedRange = range.replaceAll('N', maxPages.toString());
  final List<String> parts = normalizedRange.split(',');

  for (String part in parts) {
    part = part.trim();
    if (part.contains('-')) {
      final List<String> rangeParts = part.split('-');
      final int num1 = int.parse(rangeParts[0]) - 1;
      final int num2 = int.parse(rangeParts[1]) - 1;

      // 确定起始和结束页码
      final int start = num1 <= num2 ? num1 : num2;
      final int end = num1 <= num2 ? num2 : num1;

      // 添加页码到集合
      if (start <= maxPages) {
        final int validEnd = end <= maxPages ? end : maxPages;
        if (num1 <= num2) {
          // 正序
          for (int i = start; i <= validEnd; i++) {
            pages.add(i);
          }
        } else {
          // 倒序
          for (int i = validEnd; i >= start; i--) {
            pages.add(i);
          }
        }
      }
    } else {
      final int page = int.parse(part) - 1;
      if (page >= 0 && page < maxPages) {
        pages.add(page);
      }
    }
  }

  final List<int> result = pages.toList();
  return result;
}

/// 获取文件名中的前缀数字
int extractNumberPrefix(String filePath) {
  final fileName = PathUtils(filePath).stem;
  final match = RegExp(r'^\d+').firstMatch(fileName);
  return match != null ? int.parse(match.group(0)!) : 0;
}

/// 获取文件名中的后缀数字
int extractNumberSuffix(String filePath) {
  final fileName = PathUtils(filePath).stem;
  final match = RegExp(r'\d+$').firstMatch(fileName);
  return match != null ? int.parse(match.group(0)!) : 0;
}

/// 对文件列表进行排序
List<String> sortFiles(List<String> files, String sortBy, String direction) {
  final sortedFiles = List<String>.from(files);
  switch (sortBy) {
    case 'selection':
      return direction == 'ascending'
          ? sortedFiles
          : sortedFiles.reversed.toList();
    case 'name':
      sortedFiles.sort((a, b) {
        final comp = p.basename(a).compareTo(p.basename(b));
        return direction == 'ascending' ? comp : -comp;
      });
      break;
    case 'numberPrefix':
      sortedFiles.sort((a, b) {
        final comp = extractNumberPrefix(a).compareTo(extractNumberPrefix(b));
        return direction == 'ascending' ? comp : -comp;
      });
      break;
    case 'numberSuffix':
      sortedFiles.sort((a, b) {
        final comp = extractNumberSuffix(a).compareTo(extractNumberSuffix(b));
        return direction == 'ascending' ? comp : -comp;
      });
      break;
    case 'createDate':
      sortedFiles.sort((a, b) {
        final aStat = File(a).statSync();
        final bStat = File(b).statSync();
        // 优先使用创建时间，不可用时使用修改时间
        final aDate = aStat.accessed;
        final bDate = bStat.accessed;
        final comp = aDate.compareTo(bDate);
        return direction == 'ascending' ? comp : -comp;
      });
      break;
    case 'modDate':
      sortedFiles.sort((a, b) {
        final aStat = File(a).statSync();
        final bStat = File(b).statSync();
        final comp = aStat.modified.compareTo(bStat.modified);
        return direction == 'ascending' ? comp : -comp;
      });
      break;
  }
  return sortedFiles;
}

// 将数字转换为罗马数字
String convertToRoman(int number) {
  if (number <= 0) return '';

  const List<String> romanSymbols = [
    'I',
    'IV',
    'V',
    'IX',
    'X',
    'XL',
    'L',
    'XC',
    'C',
    'CD',
    'D',
    'CM',
    'M'
  ];
  const List<int> values = [
    1,
    4,
    5,
    9,
    10,
    40,
    50,
    90,
    100,
    400,
    500,
    900,
    1000
  ];

  String result = '';
  int i = values.length - 1;

  while (number > 0) {
    while (number >= values[i]) {
      number -= values[i];
      result += romanSymbols[i];
    }
    i--;
  }

  return result;
}

int parseTimeToMs(String time) {
  if (time.isEmpty) return 0;

  // 支持两种格式：
  // 1. HH:MM:SS.sss
  // 2. MM:SS.sss
  final regExp = RegExp(r'^(\d+):(\d{1,2})(?::(\d{1,2}(?:\.\d+)?))?$');
  final match = regExp.firstMatch(time);

  if (match == null) {
    throw FormatException('无效的时间格式: $time');
  }

  try {
    int hours = 0;
    int minutes = 0;
    double seconds = 0.0;

    // 判断时间格式类型
    if (match.group(3) != null) {
      // HH:MM:SS格式
      hours = int.parse(match.group(1)!);
      minutes = int.parse(match.group(2)!);
      seconds = double.parse(match.group(3)!);
    } else {
      // MM:SS格式
      minutes = int.parse(match.group(1)!);
      seconds = double.parse(match.group(2)!);
    }

    // 拆分秒和毫秒
    final secondsPart = seconds.toString().split('.');
    final wholeSeconds = int.parse(secondsPart[0]);
    final milliseconds = secondsPart.length > 1
        ? int.parse(secondsPart[1].padRight(3, '0').substring(0, 3))
        : 0;

    return hours * 3600000 +
        minutes * 60000 +
        wholeSeconds * 1000 +
        milliseconds;
  } catch (e) {
    throw FormatException('时间解析失败: $time - $e');
  }
}

String msToTime(int ms) {
  final duration = Duration(milliseconds: ms);
  return [
    duration.inHours,
    duration.inMinutes.remainder(60),
    duration.inSeconds.remainder(60)
  ].map((v) => v.toString().padLeft(2, '0')).join(':');
}

Future<CustomResponse> docx2txt(String path) async {
  try {
    final file = File(path);
    final bytes = await file.readAsBytes();
    final text = docxToText(bytes);
    return CustomResponse(status: "success", message: 'common.completed'.tr, data: text);
  } catch (e) {
    return CustomResponse(
        status: "error", message: "docx转txt失败", data: e.toString());
  }
}

Future<CustomResponse> getPDFPageSize(String path, String pageRange) async {
  try {
    final PdfDocument document = PdfDocument(
      inputBytes: File(path).readAsBytesSync(),
    );
    final List<int> pages = parsePageRange(pageRange, document.pages.count);
    final List<Map<String, List<double>>> pageSizes = [];
    for (int page in pages) {
      final pageSize = document.pages[page].size;
      pageSizes.add({
        "$page": [pageSize.width, pageSize.height]
      });
    }
    logger.i("pageSizes: $pageSizes");
    return CustomResponse(
        status: "success", message: 'common.completed'.tr, data: jsonEncode(pageSizes));
  } catch (e) {
    return CustomResponse(
        status: "error", message: "获取PDF页面尺寸失败", data: e.toString());
  }
}

class PathUtils {
  final String path;

  PathUtils(this.path);

  /// 获取带后缀的文件名
  String get name => p.basename(path);

  /// 获取不带后缀的文件名
  String get stem => p.basenameWithoutExtension(path);

  /// 获取父级目录
  String get parent => p.dirname(path);

  /// 获取文件扩展名（带点）
  String get extension => p.extension(path);

  /// 判断是否为文件
  bool get isFile => FileSystemEntity.isFileSync(path);

  /// 判断是否为目录
  bool get isDir => FileSystemEntity.isDirectorySync(path);

  /// 判断文件或目录是否存在
  bool exists() {
    if (isDir) {
      return Directory(path).existsSync();
    } else {
      return File(path).existsSync();
    }
  }

  /// 递归创建目录
  /// [existsOk] 如果为true，目录已存在时不会抛出异常
  Future<void> makeDirs({bool existsOk = true}) async {
    final directory = Directory(path);
    if (await directory.exists()) {
      if (!existsOk) {
        throw Exception('Directory already exists: $path');
      }
      return;
    }
    await directory.create(recursive: true);
  }

  /// 连接路径
  static String join(List<String> paths) => p.joinAll(paths);

  /// 规范化路径
  static String normalize(String path) => p.normalize(path);

  /// 获取相对路径
  static String relative(String path, {String? from}) =>
      p.relative(path, from: from);

  /// 判断路径是否为绝对路径
  static bool isAbsolute(String path) => p.isAbsolute(path);

  /// 在文件名中追加字符串，返回新文件路径
  String stemAppend(String suffix) {
    final dir = parent;
    final ext = extension;
    return p.join(dir, '$stem$suffix$ext');
  }

  /// 在文件名中追加字符串，返回新文件路径
  Future<String> stemAppendCrossPlatform(String suffix) async {
    String outputPath = "";
    if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
      outputPath = stemAppend(suffix);
    } else if (Platform.isIOS || Platform.isAndroid) {
      final ext = extension;
      outputPath = join([(await downloadDir), '$stem$suffix$ext']);
    }
    return outputPath;
  }

  /// 转换路径，返回新文件路径
  Future<String> convertPath(String mode,
      {String? stem_append, String? suffix, String? outputDir}) async {
    String outputPath = "";
    suffix ??= extension;
    outputDir ??= "";
    if (PathUtils.isDesktop) {
      switch (mode) {
        case "same":
          outputPath = p.join(parent, '$stem$stem_append$suffix');
          break;
        case "custom":
          outputPath = p.join(outputDir, '$stem$stem_append$suffix');
          break;
        case "overwrite":
          outputPath = p.join(parent, '$stem$suffix');
          break;
        default:
          throw Exception("Invalid mode: $mode");
      }
    } else if (PathUtils.isMobile) {
      outputPath =
          p.join((await downloadDir), 'Export', '$stem$stem_append$suffix');
    }
    // 确保输出目录存在
    await Directory(p.dirname(outputPath)).create(recursive: true);
    return outputPath;
  }

  /// 判断是否为旧版Android
  static Future<bool> get isOldAndroid async {
    if (Platform.isAndroid) {
      DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();
      final androidInfo = await deviceInfoPlugin.androidInfo;
      final skdVersion = androidInfo.version.sdkInt;
      logger.i("skdVersion: $skdVersion");
      return skdVersion < 33;
    }
    return false;
  }

  /// 判断是否为桌面端
  static bool get isDesktop {
    if (Platform.isWindows || Platform.isMacOS || Platform.isLinux) {
      return true;
    }
    return false;
  }

  /// 判断是否为移动端
  static bool get isMobile {
    if (Platform.isIOS || Platform.isAndroid) {
      return true;
    }
    return false;
  }

  /// 获取下载目录路径
  static Future<String> get downloadDir async {
    if (Platform.isWindows) {
      return p.join(Platform.environment['USERPROFILE']!, 'Downloads');
    } else if (Platform.isMacOS) {
      return p.join(Platform.environment['HOME']!, 'Downloads');
    } else if (Platform.isLinux) {
      return p.join(Platform.environment['HOME']!, 'Downloads');
    } else if (Platform.isIOS) {
      final dir = await getApplicationDocumentsDirectory();
      return dir.path;
    } else if (Platform.isAndroid) {
      if (await isOldAndroid) {
        final dir = await getExternalStorageDirectory();
        return dir!.path;
      } else {
        final dir = await ExternalPath.getExternalStoragePublicDirectory(
            ExternalPath.DIRECTORY_DOWNLOADS);
        return dir;
      }
    }
    return "";
  }

  // 获取临时目录路径
  static Future<String> get tempDir async {
    final dir = await getTemporaryDirectory();
    return dir.path;
  }

  static Future<String> which(String command) async {
    final envPath =
        Platform.environment['PATH']?.split(Platform.isWindows ? ';' : ':');
    if (envPath != null) {
      for (var path in envPath) {
        if (Platform.isWindows) {
          // String searchPath = '$path\\$command.exe';
          String searchPath = PathUtils.join([path, "$command.exe"]);
          if (PathUtils(searchPath).exists()) {
            return searchPath;
          }
          searchPath = PathUtils.join([path, command]);
          if (PathUtils(searchPath).exists()) {
            return searchPath;
          }
        } else {
          String searchPath = PathUtils.join([path, command]);
          if (PathUtils(searchPath).exists()) {
            return searchPath;
          }
        }
      }
    }
    return "";
  }

  static Future<String> getOutputApkgPath({String? name}) async {
    final settingController = Get.find<SettingController>();
    final now = DateTime.now();
    final timestamp =
        "${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}${now.hour.toString().padLeft(2, '0')}${now.minute.toString().padLeft(2, '0')}${now.second.toString().padLeft(2, '0')}${now.millisecond.toString().padLeft(3, '0')}";
    String filename = name ?? "output";

    filename = filename.replaceAll(RegExp(r'[<>:"/\\|?*]'),
        '_'); // Replace invalid filename characters with underscore
    filename = "${filename}_$timestamp.apkg";
    if (Platform.isIOS) {
      final downloadDir = (await getApplicationDocumentsDirectory()).path;
      return p.join(downloadDir, "Export", filename);
    } else if (Platform.isAndroid) {
      DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();
      final androidInfo = await deviceInfoPlugin.androidInfo;
      final skdVersion = androidInfo.version.sdkInt;
      logger.i("skdVersion: $skdVersion");
      if (skdVersion >= 33) {
        final _storage = StorageManager();
        final parentDir =
            _storage.read(StorageBox.default_, AnkiStorageKeys.outputDir, "");
        logger.i("parentDir: ${parentDir}");
        if (parentDir.isNotEmpty) {
          return p.join(parentDir, filename);
        } else {
          return p.join((await downloadDir), filename);
        }
      } else {
        final dir = await getExternalStorageDirectory();
        return p.join(dir!.path, "Export", filename);
      }
    } else if (Platform.isMacOS || Platform.isWindows || Platform.isLinux) {
      if (settingController.outputDir.value.isNotEmpty) {
        return p.join(settingController.outputDir.value, filename);
      } else {
        return p.join((await downloadDir), filename);
      }
    }
    return "";
  }

  static Future<String> getTempFilePath(String suffix) async {
    String filename = "${Ulid()}$suffix";
    return p.join((await tempDir), filename);
  }

  static Future<String> getNativeLibraryDir() async {
    if (Platform.isAndroid) {
      const platform = MethodChannel('samples.flutter.dev/battery');
      final path = await platform.invokeMethod<dynamic>(
        'getNativeLibraryDir',
        <String, dynamic>{},
      );
      return path;
    }
    return "";
  }
}

class TimeUtils {
  /// 将 DateTime 转换为指定格式的字符串
  /// [format] 可选的格式，默认为 'yyyy-MM-dd HH:mm:ss'
  static String formatDateTime(DateTime dateTime,
      {String format = 'yyyy-MM-dd HH:mm:ss'}) {
    final year = dateTime.year.toString();
    final month = dateTime.month.toString().padLeft(2, '0');
    final day = dateTime.day.toString().padLeft(2, '0');
    final hour = dateTime.hour.toString().padLeft(2, '0');
    final minute = dateTime.minute.toString().padLeft(2, '0');
    final second = dateTime.second.toString().padLeft(2, '0');
    final millisecond = dateTime.millisecond.toString().padLeft(3, '0');

    return format
        .replaceAll('yyyy', year)
        .replaceAll('MM', month)
        .replaceAll('dd', day)
        .replaceAll('HH', hour)
        .replaceAll('mm', minute)
        .replaceAll('ss', second)
        .replaceAll('SSS', millisecond);
  }

  /// 获取当前时间戳（毫秒）
  static int get currentTimeMillis => DateTime.now().millisecondsSinceEpoch;

  /// 获取当前时间戳（秒）
  static int get currentTimeSeconds =>
      DateTime.now().millisecondsSinceEpoch ~/ 1000;

  /// 将时间戳（毫秒）转换为 DateTime
  static DateTime fromMilliseconds(int milliseconds) {
    return DateTime.fromMillisecondsSinceEpoch(milliseconds);
  }

  /// 将时间戳（秒）转换为 DateTime
  static DateTime fromSeconds(int seconds) {
    return DateTime.fromMillisecondsSinceEpoch(seconds * 1000);
  }

  /// 获取相对时间描述（例如：刚刚、x分钟前、x小时前等）
  static String getRelativeTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inSeconds < 60) {
      return '刚刚';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}分钟前';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}小时前';
    } else if (difference.inDays < 30) {
      return '${difference.inDays}天前';
    } else if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      return '$months个月前';
    } else {
      final years = (difference.inDays / 365).floor();
      return '$years年前';
    }
  }

  /// 判断是否为同一天
  static bool isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  /// 获取指定日期的开始时间
  static DateTime startOfDay(DateTime dateTime) {
    return DateTime(dateTime.year, dateTime.month, dateTime.day);
  }

  /// 获取指定日期的结束时间
  static DateTime endOfDay(DateTime dateTime) {
    return DateTime(
      dateTime.year,
      dateTime.month,
      dateTime.day,
      23,
      59,
      59,
      999,
      999,
    );
  }

  /// 获取指定月份的天数
  static int getDaysInMonth(int year, int month) {
    return DateTime(year, month + 1, 0).day;
  }

  /// 格式化持续时间（毫秒）
  static String formatDuration(int milliseconds) {
    final duration = Duration(milliseconds: milliseconds);
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '${hours}h${minutes}m${seconds}s';
    } else if (minutes > 0) {
      return '${minutes}m${seconds}s';
    } else {
      return '${seconds}s';
    }
  }

  /// 解析时间字符串为 DateTime
  /// 支持格式：yyyy-MM-dd HH:mm:ss、yyyy/MM/dd HH:mm:ss、yyyy.MM.dd HH:mm:ss
  static DateTime? parse(String dateString) {
    try {
      // 移除所有非数字字符
      final cleanString = dateString.replaceAll(RegExp(r'[^\d]'), '');

      if (cleanString.length < 8) return null;

      final year = int.parse(cleanString.substring(0, 4));
      final month = int.parse(cleanString.substring(4, 6));
      final day = int.parse(cleanString.substring(6, 8));

      int hour = 0;
      int minute = 0;
      int second = 0;

      if (cleanString.length >= 14) {
        hour = int.parse(cleanString.substring(8, 10));
        minute = int.parse(cleanString.substring(10, 12));
        second = int.parse(cleanString.substring(12, 14));
      }

      return DateTime(year, month, day, hour, minute, second);
    } catch (e) {
      return null;
    }
  }
}

class AnkiNote {
  final String deckName;
  final String modelName;
  final List<String> fields;
  List<String> tags;
  String guid;
  List<String> mediaList = [];

  AnkiNote({
    required this.deckName,
    required this.modelName,
    required this.fields,
    String? guid,
    List<String>? tags,
    List<String>? mediaList,
  })  : guid = guid ?? "",
        tags = tags ?? [], // 使用级联操作符初始化
        mediaList = mediaList ?? [];

  Map<String, dynamic> toJson() {
    return {
      'deckName': deckName,
      'modelName': modelName,
      'fields': fields,
      'tags': tags,
      'guid': guid,
      'mediaList': mediaList,
    };
  }

  @override
  String toString() {
    return 'AnkiNote(deckName: $deckName, modelName: $modelName, fields: ${fields.join(', ')}, tags: ${tags?.join(', ')}, guid: $guid, mediaList: $mediaList)';
  }
}

/// 扩展 List 类型，添加 groupBy 方法
extension ListExtension<T> on List<T> {
  /// 将列表按照给定的 key 函数分组
  Map<K, List<T>> groupBy<K>(K Function(T) keyFunction) {
    final result = <K, List<T>>{};
    for (var element in this) {
      final key = keyFunction(element);
      result.putIfAbsent(key, () => []).add(element);
    }
    return result;
  }
}

// 定义节点结构，用于构建文档树
class DocNode {
  int level;
  String line;
  int position;
  List<String> contentAfter = [];
  List<DocNode> children = [];
  bool isLeafNode = true; // 默认为叶子节点，后续计算
  bool processForLevel = false; // 是否是目标级别或叶子节点

  DocNode(this.level, this.line, this.position);

  @override
  String toString() =>
      'DocNode(level: $level, line: $line, position: $position, contentAfter: [${contentAfter.length} lines], children: ${children.length}, isLeafNode: $isLeafNode, processForLevel: $processForLevel)';
}

class AnkiConnectController extends GetxController {
  final parentDeckList = <String>["Guru导入", "系统默认", "测试"].obs;
  final tagsList = <String>["Guru导入"].obs;
  final modelList = defaultFieldListMap.keys.toList().obs;
  final fieldList = <String>[].obs;
  final fieldListMap = defaultFieldListMap.obs;
  final settingController = Get.find<SettingController>();
  static const defaultFieldListMap = {
    "Kevin Text QA Card v2": ["Front", "Back", "Hint", "Notes", "Source"],
    "Kevin Image Cloze v5": [
      "ID",
      "Header",
      "Image",
      "Text",
      "Masks",
      "Source",
      "Notes",
      "Mode",
      "Index",
      "Colors",
      "Reversed"
    ],
    "Image Occlusion": [
      "Occlusion",
      "Image",
      "Header",
      "Back Extra",
      "Comments"
    ],
    "Kevin Mindmap Card v3": ["Front", "Back", "Source", "Notes"],
    "Kevin Text Cloze v3": ["ID", "Front", "Back", "Mode", "Index"],
    "Kevin Choice Card v2": [
      "Question",
      "Options",
      "Answers",
      "Remarks",
      "Notes",
      "Source",
      "Extra",
    ],
    "Kevin Reader Card v2": ["原文", "出处", "备注", "标题", "笔记", "反转"],
    "Kevin Vocab Card v2": ["Word", "Data", "Note"],
    "Kevin Image QA Card v2": [
      "ID",
      "Header",
      "Front",
      "Back",
      "Source",
      "Masks",
      "Notes",
      "FrontText",
      "BackText",
      "Mode"
    ],
  };

  /// 将LaTeX公式从Markdown格式转换为Anki兼容格式
  ///
  /// [content] - 包含LaTeX公式的原文本
  ///
  /// 转换规则：
  /// - 块级公式：$$xx$$ -> \[xx\]
  /// - 行内公式：$xx$ -> \(xx\)
  ///
  /// 返回转换后的文本
  String convertLatexFormula(String content) {
    // 转换块级公式 $$xx$$ -> \[xx\]
    final blockRegex = RegExp(r'\$\$([\s\S]*?)\$\$');
    String result = content.replaceAllMapped(blockRegex, (match) {
      return '\\[${match.group(1)}\\]';
    });

    // 转换行内公式 $xx$ -> \(xx\)
    // 使用正则模拟否定前后查找，确保不会匹配到已转换块级公式中的单个$
    final inlineRegex = RegExp(r'(^|[^$])\$([^$][\s\S]*?[^$])\$([^$]|$)');
    result = result.replaceAllMapped(inlineRegex, (match) {
      // group(1)是$前的字符，group(2)是公式内容，group(3)是$后的字符
      return '${match.group(1)}\\(${match.group(2)}\\)${match.group(3)}';
    });

    return result;
  }

  Future<bool> resetAnkiConnectData(
      {List<String> data = const ['deck', 'tag', 'model']}) async {
    try {
      final settingController = Get.find<SettingController>();
      if (settingController.cardMode.value == "ankiconnect") {
        final futures = <Future>[];
        if (data.contains('deck')) {
          futures.add(getDeckNames().then((value) {
            parentDeckList.value = value;
          }));
        }
        if (data.contains('tag')) {
          futures.add(getTags().then((value) => tagsList.value = value));
        }
        if (data.contains('model')) {
          futures.add(getModelNames().then((value) => modelList.value = value));
        }
        await Future.wait(futures);
      } else if (settingController.cardMode.value == "ankidroid") {
        const platform = MethodChannel('samples.flutter.dev/battery');

        // 检查AnkiDroid API是否可用
        final isApiAvailable =
            await platform.invokeMethod<bool>('isApiAvailable');
        logger.i("isApiAvailable: $isApiAvailable");
        if (isApiAvailable == null || !isApiAvailable) {
          // API不可用时使用默认值
          parentDeckList.value = ["Guru导入", "系统默认", "测试"];
          tagsList.value = ["Guru导入"];
          modelList.value = defaultFieldListMap.keys.toList();
          return false;
        }

        final futures = <Future>[];

        if (data.contains('deck')) {
          futures.add(
              platform.invokeMethod<List<dynamic>>('getDeckList').then((value) {
            logger.i("getDeckList: $value");
            parentDeckList.value = value?.cast<String>() ?? [];
          }));
        }

        if (data.contains('model')) {
          futures.add(platform
              .invokeMethod<List<dynamic>>('getModelList')
              .then((value) {
            logger.i("getModelList: $value");
            modelList.value = value?.cast<String>() ?? [];
          }));
        }

        if (data.contains('tag')) {
          // AnkiDroid API doesn't provide tag list functionality, use default
          tagsList.value = ["Guru导入"];
        }

        await Future.wait(futures);
      } else {
        parentDeckList.value = ["Guru导入", "系统默认", "测试"];
        tagsList.value = ["Guru导入"];
        modelList.value = defaultFieldListMap.keys.toList();
        fieldList.value = defaultFieldListMap[modelList[0]]!;
        if (Platform.isAndroid) {
          const platform = MethodChannel('samples.flutter.dev/battery');

          // 检查AnkiDroid API是否可用
          final isApiAvailable =
              await platform.invokeMethod<bool>('isApiAvailable');
          logger.i("isApiAvailable: $isApiAvailable");
          if (isApiAvailable == null || !isApiAvailable) {
            // API不可用时使用默认值
            parentDeckList.value = ["Guru导入", "系统默认", "测试"];
            tagsList.value = ["Guru导入"];
            modelList.value = defaultFieldListMap.keys.toList();
            return false;
          }

          final futures = <Future>[];

          if (data.contains('deck')) {
            futures.add(platform
                .invokeMethod<List<dynamic>>('getDeckList')
                .then((value) {
              logger.i("getDeckList: $value");
              parentDeckList.value = value?.cast<String>() ?? [];
            }));
          }

          if (data.contains('model')) {
            futures.add(platform
                .invokeMethod<List<dynamic>>('getModelList')
                .then((value) {
              logger.i("getModelList: $value");
              modelList.value = value?.cast<String>() ?? [];
            }));
          }

          if (data.contains('tag')) {
            // AnkiDroid API doesn't provide tag list functionality, use default
            tagsList.value = ["Guru导入"];
          }

          await Future.wait(futures);
        }
      }
      return true;
    } catch (e) {
      showToastNotification(
          null, "AnkiConnect连接失败", "请检查: 1.Anki是否打开; 2.AnkiConnect插件是否安装",
          type: "error");
      return false;
    }
  }

  Future<List<String>> updateFieldList(String modelName) async {
    try {
      final settingController = Get.find<SettingController>();
      if (settingController.cardMode.value == "ankiconnect") {
        fieldList.value = await getModelFieldNames(modelName);
      } else if (settingController.cardMode.value == "apkg") {
        fieldList.value = defaultFieldListMap[modelName]!;
      }
      // 通知所有监听器更新
      update();
      return fieldList.value;
    } catch (e, stack) {
      logger.e("AnkiConnectController.updateFieldList error:",
          error: e, stackTrace: stack);
      return [];
    }
  }

  Future<List<String>> getDeckNames() async {
    final dio = Dio();
    final settingController = Get.find<SettingController>();
    final address = settingController.ankiConnectUrl.value;
    final response =
        await dio.post(address, data: {"action": "deckNames", "version": 6});
    if (response.data['error'] != null) {
      throw Exception(response.data['error']);
    }
    final List<String> deckNames = List<String>.from(response.data['result']);
    return deckNames;
  }

  Future<List<String>> getModelNames() async {
    final dio = Dio();
    final settingController = Get.find<SettingController>();
    final address = settingController.ankiConnectUrl.value;

    final response =
        await dio.post(address, data: {"action": "modelNames", "version": 6});
    final List<String> modelNames = List<String>.from(response.data['result']);
    return modelNames;
  }

  Future<dynamic> getModelTemplates(String modelName) async {
    final dio = Dio();
    final settingController = Get.find<SettingController>();
    final address = settingController.ankiConnectUrl.value;

    final response = await dio.post(address, data: {
      "action": "modelTemplates",
      "params": {"modelName": modelName},
      "version": 6,
    });
    return response;
  }

  Future<dynamic> getModelNamesAndIds() async {
    final dio = Dio();
    final settingController = Get.find<SettingController>();
    final address = settingController.ankiConnectUrl.value;

    final response = await dio.post(address, data: {
      "action": "modelNamesAndIds",
      "version": 6,
    });
    return response;
  }

  Future<List<String>> getModelFieldNames(String modelName) async {
    try {
      final dio = Dio();
      final settingController = Get.find<SettingController>();
      final address = settingController.ankiConnectUrl.value;
      final response = await dio.post(address, data: {
        "action": "modelFieldNames",
        "version": 6,
        "params": {"modelName": modelName}
      });
      final List<String> fieldNames =
          List<String>.from(response.data['result']);
      return fieldNames;
    } catch (e) {
      logger.e("getModelFieldNames error: $e");
      return Future.value(defaultFieldListMap[modelName]);
    }
  }

  Future<List<String>> getTags() async {
    final dio = Dio();
    final settingController = Get.find<SettingController>();
    final address = settingController.ankiConnectUrl.value;
    final response =
        await dio.post(address, data: {"action": "getTags", "version": 6});
    final List<String> tags = List<String>.from(response.data['result']);
    return tags;
  }

  Future<List<dynamic>> getNotesInfo(List<int> noteIds) async {
    final dio = Dio();
    final settingController = Get.find<SettingController>();
    final address = settingController.ankiConnectUrl.value;
    final response = await dio.post(address, data: {
      "action": "notesInfo",
      "version": 6,
      "params": {"notes": noteIds}
    });
    final notes = List<dynamic>.from(response.data['result']);
    return notes;
  }

  Future<List<dynamic>> findNotes(String query) async {
    final dio = Dio();
    final settingController = Get.find<SettingController>();
    final address = settingController.ankiConnectUrl.value;
    final response = await dio.post(address, data: {
      "action": "findNotes",
      "version": 6,
      "params": {"query": query}
    });
    final List<int> noteIds = List<int>.from(response.data['result']);
    final notes = await getNotesInfo(noteIds);
    return notes;
  }

  Future<List<dynamic>> getCardsInfo(List<int> noteIds) async {
    final dio = Dio();
    final settingController = Get.find<SettingController>();
    final address = settingController.ankiConnectUrl.value;
    final response = await dio.post(address, data: {
      "action": "cardsInfo",
      "version": 6,
      "params": {"cards": noteIds}
    });
    final notes = List<dynamic>.from(response.data['result']);
    return notes;
  }

  Future<List<dynamic>> findCards(String query) async {
    final dio = Dio();
    final settingController = Get.find<SettingController>();
    final address = settingController.ankiConnectUrl.value;
    final response = await dio.post(address, data: {
      "action": "findCards",
      "version": 6,
      "params": {"query": query}
    });
    final List<int> noteIds = List<int>.from(response.data['result']);
    final notes = await getCardsInfo(noteIds);
    return notes;
  }

  Future<void> storeMediaFile(String path) async {
    final dio = Dio();
    final settingController = Get.find<SettingController>();
    final address = settingController.ankiConnectUrl.value;
    final filename = PathUtils(path).name;
    await dio.post(address, data: {
      "action": "storeMediaFile",
      "version": 6,
      "params": {"filename": filename, "path": path}
    });
  }

  Future<dynamic> updateNote(Map<String, dynamic> note) async {
    final dio = Dio();
    final settingController = Get.find<SettingController>();
    final address = settingController.ankiConnectUrl.value;
    final response = await dio.post(address, data: {
      "action": "updateNote",
      "version": 6,
      "params": {"note": note}
    });
    return response;
  }

  Future<dynamic> importApkg(String path, {bool isDelete = false}) async {
    final dio = Dio();
    final settingController = Get.find<SettingController>();
    final address = settingController.ankiConnectUrl.value;
    final response = await dio.post(address, data: {
      "action": "importPackage",
      "version": 6,
      "params": {"path": path}
    });
    if (isDelete) {
      try {
        await File(path).delete();
      } catch (e) {
        logger.e("delete file error: $e");
      }
    }
    return response;
  }

  Future<dynamic> exportApkg(
      String deckName, String path, bool includeSched) async {
    final dio = Dio();
    final settingController = Get.find<SettingController>();
    final address = settingController.ankiConnectUrl.value;
    final response = await dio.post(address, data: {
      "action": "exportPackage",
      "version": 6,
      "params": {"deck": deckName, "path": path, "includeSched": includeSched}
    });
    return response;
  }

  Future<dynamic> getMediaDir() async {
    final dio = Dio();
    final settingController = Get.find<SettingController>();
    final address = settingController.ankiConnectUrl.value;
    final response = await dio.post(address, data: {
      "action": "getMediaDirPath",
      "version": 6,
    });
    if (response.data['error'] != null) {
      throw Exception(response.data['error']);
    }
    return response.data['result'];
  }

  Future<dynamic> exportJson(String deckName, String path) async {
    try {
      final List<Map<String, dynamic>> resultCards = [];
      final List<dynamic> cardsInfo = await findCards('deck:"$deckName"');
      for (var i = 0; i < cardsInfo.length; i++) {
        final card = cardsInfo[i];

        // Convert fields format
        final Map<String, String> fields = {};
        final originalFields = card['fields'] as Map<String, dynamic>;
        originalFields.forEach((key, value) {
          fields[key] = value['value'] as String;
        });
        // logger.i(card);
        final cardObj = {
          'id': card['cardId'],
          'deckName': card['deckName'],
          'modelName': card['modelName'],
          'createdTime': card['note'] != null
              ? DateTime.fromMillisecondsSinceEpoch(card['note'])
                  .toIso8601String()
              : null,
          'updatedTime': card['mod'] != null
              ? DateTime.fromMillisecondsSinceEpoch(card['mod'] * 1000)
                  .toIso8601String()
              : null,
          ...fields,
        };
        resultCards.add(cardObj);
      }

      // Write to file
      final jsonString = jsonEncode(resultCards);
      await File(path).writeAsString(jsonString);

      return {'result': true, 'count': resultCards.length, 'path': path};
    } catch (e, stack) {
      logger.e("Export JSON error: $e\n$stack");
      return {'result': false, 'error': e.toString()};
    }
  }

  Future<RustResponse> genApkg(
      List<AnkiNote> notes, List<String> mediaList, String? outputPath,
      {List<String> internalMediaTypes = const []}) async {
    outputPath ??= await PathUtils.getOutputApkgPath();
    logger.d("output_path: $outputPath");
    final settingController = Get.find<SettingController>();
    final exportMode = settingController.cardMode.value;
    final address = settingController.ankiConnectUrl.value;
    List<String> allMediaList = [];
    allMediaList.addAll(mediaList);
    for (final note in notes) {
      allMediaList.addAll(note.mediaList);
    }
    // 去重
    allMediaList = allMediaList.toSet().toList();
    // logger.w("allMediaList: $allMediaList");
    final data = {
      'notes': notes.map((e) => e.toJson()).toList(),
      'media_list': allMediaList,
      "output_path": outputPath,
      "address": exportMode == "ankiconnect" ? address : null,
      "show_progress": false,
      "internal_media_types": internalMediaTypes,
    };
    final messageController = Get.find<MessageController>();
    final resp = await messageController.request(data, "anki/gen_apkg");
    return resp;
  }

  // 添加媒体文件
  Future<CustomResponse> addMediaToAnkiDroid(String path,
      {String? preferredName = "image", String? mimeType = "image"}) async {
    const platform = MethodChannel('samples.flutter.dev/battery');
    // 检查api是否可用
    final isApiAvailable = await platform.invokeMethod<bool>(
      'isApiAvailable',
    );
    if (isApiAvailable == null || !isApiAvailable) {
      return CustomResponse(
          status: "error", message: "AnkiDroid API 不可用", data: "");
    }
    String mediaRes = await platform.invokeMethod<dynamic>(
      'addMedia',
      <String, dynamic>{
        'fileUri': path,
        'preferredName': preferredName,
        'mimeType': mimeType,
      },
    );
    return CustomResponse(status: "success", message: 'common.completed'.tr, data: mediaRes);
  }

  Future<CustomResponse> uploadToAnkiDroid(List<AnkiNote> notes) async {
    try {
      logger.i("notes: $notes");
      const platform = MethodChannel('samples.flutter.dev/battery');
      // 检查api是否可用
      final isApiAvailable = await platform.invokeMethod<bool>(
        'isApiAvailable',
      );
      logger.i(isApiAvailable);
      if (isApiAvailable == null || !isApiAvailable) {
        return CustomResponse(
            status: "error", message: "AnkiDroid API 不可用", data: "");
      }
      // 添加卡片
      // 把notes按照相同deckName和modelName的分为一组
      final groupedNotes =
          notes.groupBy((e) => "${e.deckName}||${e.modelName}");
      for (final group in groupedNotes.entries) {
        final result = await platform.invokeMethod<dynamic>(
          'addNotes',
          <String, dynamic>{
            'deckName': group.key.split('||')[0],
            'modelName': group.key.split('||')[1],
            "fieldsList": group.value.map((e) => e.fields).toList(),
            'tagsList': group.value.map((e) => e.tags ?? []).toList(),
          },
        );
      }
      return CustomResponse(status: "success", message: 'common.completed'.tr, data: "");
    } catch (e) {
      logger.e("uploadToAnkiDroid error: $e");
      if (e is PlatformException) {
        return CustomResponse(
            status: "error", message: e.message ?? "", data: "");
      }
      return CustomResponse(status: "error", message: e.toString(), data: "");
    }
  }

  /// 将文本中的挖空标记转换为 Anki 挖空格式 [[c1::xx]]
  ///
  /// [text] 原始文本
  /// [clozeGrammarList] 挖空语法列表,如 ["[[xx]]", "{{xx}}"]
  /// [ignoreGroup] 是否忽略原有分组
  ///   - true: 对所有挖空重新编号
  ///   - false: 标准格式保持原编号,自定义格式统一使用c1
  ///
  /// 返回转换后的文本
  String convertCloze(String text, List<String> clozeGrammarList,
      {bool ignoreGroup = true}) {
    if (text.isEmpty || clozeGrammarList.isEmpty) return text;

    // 收集所有匹配项
    final allMatches = <_ClozeMatch>[];

    for (final grammar in clozeGrammarList) {
      // 处理 [[c1::xx]] 格式
      if (grammar == "[[c1::xx]]") {
        final regex = RegExp(r'\[\[c(\d+)::(.*?)\]\]');
        final matches = regex.allMatches(text);

        for (var match in matches) {
          allMatches.add(_ClozeMatch(
            fullMatch: match.group(0)!,
            groupNum: match.group(1)!,
            content: match.group(2)!,
            start: match.start,
            isStandard: true,
          ));
        }
        continue;
      }

      // 处理 {{c1::xx}} 格式
      if (grammar == "{{c1::xx}}") {
        final regex = RegExp(r'\{\{c(\d+)::(.*?)\}\}');
        final matches = regex.allMatches(text);

        for (var match in matches) {
          allMatches.add(_ClozeMatch(
            fullMatch: match.group(0)!,
            groupNum: match.group(1)!,
            content: match.group(2)!,
            start: match.start,
            isStandard: true,
          ));
        }
        continue;
      }

      // 处理自定义挖空语法
      final escaped = RegExp.escape(grammar);
      final pattern = escaped.replaceAll('xx', r'(.*?)');
      final regex = RegExp(pattern);
      final matches = regex.allMatches(text);

      for (var match in matches) {
        allMatches.add(_ClozeMatch(
          fullMatch: match.group(0)!,
          content: match.group(1)!,
          start: match.start,
          isStandard: false,
        ));
      }
    }

    if (allMatches.isEmpty) return text;

    // 按位置排序,确保替换顺序
    allMatches.sort((a, b) => b.start.compareTo(a.start)); // 从后往前排序

    // 构建替换映射并直接替换
    var result = text;
    var index = allMatches.length; // 从后往前计数

    for (var match in allMatches) {
      final replacement = match.isStandard
          ? (ignoreGroup
              ? '[[c$index::${match.content}]]'
              : '[[c${match.groupNum}::${match.content}]]')
          : (ignoreGroup
              ? '[[c$index::${match.content}]]'
              : '[[c1::${match.content}]]');

      // 使用 substring 进行精确替换
      final before = result.substring(0, match.start);
      final after = result.substring(match.start + match.fullMatch.length);
      result = before + replacement + after;

      if (ignoreGroup) {
        index--;
      }
    }

    return result;
  }

  Future<String> readFileWithFallback(String path) async {
    final messageController = Get.find<MessageController>();
    final data = {
      'path': path,
      'show_progress': false,
    };
    final resp = await messageController.request(data, 'read_file_auto_decode');
    if (resp.status == 'success') {
      return resp.data;
    } else {
      logger.e("readFileWithFallback error: ${resp.message}");
      return "";
    }
  }

  /// 将文本按照规则分割成不同牌组的内容
  ///
  /// [docPath] - 文档路径
  /// [splitRegex] - 分割题目的正则表达式
  /// [parentDeck] - 父牌组名称
  /// [deckPrefix] - 子牌组标识前缀，如 "@@@"
  ///
  /// Returns: Map<String, List<String>> - key为牌组名，value为该牌组下的题目列表
  Future<Map<String, List<String>>> splitText(
    String docPath,
    String splitRegex,
    String parentDeck,
    String? deckPrefix,
  ) async {
    // 读取文档内容
    String content = await readFileWithFallback(docPath);
    // 删除多余换行
    content = removeExtraNewlines(content);
    final lines = content.split('\n');

    // 编译正则表达式
    final splitPattern = RegExp(splitRegex);

    final result = <String, List<String>>{};
    var currentDeck = parentDeck;
    var currentText = StringBuffer();

    // 处理子牌组标识
    final isUsingPrefix = deckPrefix != null && deckPrefix.isNotEmpty;

    var currentLevelDecks = [parentDeck];
    var lastLevel = 0;

    for (var i = 0; i < lines.length; i++) {
      final line = lines[i];

      if (isUsingPrefix && line.trim().startsWith(deckPrefix)) {
        // 如果有未处理的文本，先保存
        if (currentText.toString().trim().isNotEmpty) {
          result
              .putIfAbsent(currentDeck, () => [])
              .add(currentText.toString().trim());
          currentText.clear();
        }

        // 计算子牌组层级
        // final prefixChar = deckPrefix[0];
        // final level = line.split('').takeWhile((c) => c == prefixChar).length;
        final prefixChar = RegExp.escape(deckPrefix[0]);
        final regex = RegExp('^($prefixChar+)');
        final match = regex.firstMatch(line.trim());
        final level = match != null ? match.group(1)!.length - 2 : 0;

        // 提取并清理子牌组名称
        final deckName = line.trim().replaceFirst(regex, '').trim();
        logger.i(
            "line: $line, level: $level, currentLevelDecks: $currentLevelDecks");
        // 更新当前层级的牌组
        if (level <= lastLevel) {
          // 如果是同级或更浅的层级，从父牌组开始重新构建
          currentLevelDecks = currentLevelDecks.sublist(0, level);
        }

        if (currentLevelDecks.isEmpty) {
          currentLevelDecks = [parentDeck];
        }

        currentLevelDecks.add(deckName);
        lastLevel = level;
        currentDeck = currentLevelDecks.join('::');

        logger.d('Level: $level, Deck: $currentDeck');
        continue;
      }

      // 检查是否是新题目的开始
      if (splitPattern.hasMatch(line) &&
          currentText.toString().trim().isNotEmpty) {
        result
            .putIfAbsent(currentDeck, () => [])
            .add(currentText.toString().trim());
        currentText.clear();
      }

      currentText.write(line);
      currentText.write('\n');

      // 处理最后一行
      if (i == lines.length - 1 && currentText.toString().trim().isNotEmpty) {
        result
            .putIfAbsent(currentDeck, () => [])
            .add(currentText.toString().trim());
      }
    }

    return result;
  }

  /// 增强版：支持带别名的链接格式 [[显示文本|实际文件]]，并处理路径中的空格
  String convertObsidianLinksEnhanced(String content) {
    // 处理带别名的图片链接 ![[显示文字|filename with space.png]]
    String result = content.replaceAllMapped(
      RegExp(r'!\[\[(?!(.*?)c\d+::)([^|]+)\|([^\]]+)\]\]', multiLine: true),
      (Match m) => '![${m.group(2)}](<${m.group(3)}>)',
    );

    // 处理普通带别名链接 [[显示文字|file name.md]]（排除包含c\d+::的情况）
    result = result.replaceAllMapped(
      RegExp(r'\[\[(?!(.*?)c\d+::)([^|]+)\|([^\]]+)\]\]', multiLine: true),
      (Match m) => '[${m.group(2)}](<${m.group(3)}>)',
    );

    // 处理简单图片链接 ![[filename]]（排除c\d+::格式）
    result = result.replaceAllMapped(
      RegExp(r'!\[\[(?!(.*?)c\d+::)([^\]]+)\]\]', multiLine: true),
      (Match m) => '![${m.group(2)}](<${m.group(2)}>)',
    );

    // 处理简单普通链接 [[filename]]（排除c\d+::格式）
    result = result.replaceAllMapped(
      RegExp(r'\[\[(?!(.*?)c\d+::)([^\]]+)\]\]', multiLine: true),
      (Match m) => '[${m.group(2)}](<${m.group(2)}>)',
    );

    return result;
  }

  /// 移除HTML根节点的<p>标签
  Future<String> removeRootPTag(String html) async {
    final messageController = Get.find<MessageController>();
    final data = {
      'html': html,
      'show_progress': false,
    };

    final resp = await messageController.request(data, 'html/remove_root_p');
    if (resp.status == 'success') {
      return resp.data;
    } else {
      // throw Exception(resp.message);
      logger.e("removeRootPTag error: ${resp.message}");
      return html;
    }
  }

  /// docx转html
  Future<String> docx2html(String path, {bool escapeLatexForJs = false}) async {
    final messageController = Get.find<MessageController>();
    final outputPath = await PathUtils.getTempFilePath('.html');
    final data = {
      'docx_path': path,
      'output_path': outputPath,
      'show_progress': false,
      'escape_latex_for_js': escapeLatexForJs,
    };

    final resp = await messageController.request(data, 'docx_to_html');
    if (resp.status == 'success') {
      return resp.data;
    } else {
      // throw Exception(resp.message);
      logger.e("docx2html error: ${resp.message}");
      return "";
    }
  }

  /// 删除多余换行
  /// [text] - 需要处理的文本
  /// [threshold] - 允许的最大连续空行数
  /// 返回处理后的文本
  String removeExtraNewlines(String text, {int threshold = 2}) {
    if (text.isEmpty || threshold < 1) return text;

    // 使用正则表达式匹配连续的空行
    // \n{threshold+} 表示匹配 threshold 个或更多的换行符
    final pattern = RegExp('\n{${threshold + 1},}');

    // 将匹配到的连续空行替换为 threshold 个换行符
    return text.replaceAll(pattern, '\n' * threshold);
  }

  Future<AnkiNote> convertMD2HTML(
      AnkiNote note, List<int> fieldIds, String docPath,
      {String? mediaDir, bool convertObsidianLinks = false}) async {
    for (final fieldId in fieldIds) {
      try {
        String content = note.fields[fieldId];
        if (convertObsidianLinks) {
          content = convertObsidianLinksEnhanced(content);
        }

        // 1. 先用占位符替换公式
        // 块级公式
        final blockFormulas = <String>[];
        content = content.replaceAllMapped(
          RegExp(r'\$\$(.+?)\$\$', dotAll: true),
          (m) {
            blockFormulas.add(m.group(1)!);
            return '|||BLOCK_FORMULA_${blockFormulas.length - 1}|||';
          },
        );
        // 行内公式
        final inlineFormulas = <String>[];
        content = content.replaceAllMapped(
          RegExp(r'(?<!\$)\$(?!\$)(.+?)(?<!\$)\$(?!\$)', dotAll: true),
          (m) {
            inlineFormulas.add(m.group(1)!);
            return '|||INLINE_FORMULA_${inlineFormulas.length - 1}|||';
          },
        );

        // 2. markdown转html
        final html0 = markdownToHtml(content,
            blockSyntaxes: const [
              FencedCodeBlockSyntax(),
              TableSyntax(),
            ],
            inlineSyntaxes: [
              InlineHtmlSyntax(),
              StrikethroughSyntax(),
              EmojiSyntax(),
              ColorSwatchSyntax(),
              AutolinkExtensionSyntax(),
            ],
            extensionSet: ExtensionSet.gitHubWeb);

        // 3. 再把占位符替换回公式
        String html = html0;
        for (int i = 0; i < blockFormulas.length; i++) {
          html = html.replaceAll(
              '|||BLOCK_FORMULA_${i}|||', '\\[${blockFormulas[i]}\\]');
        }
        for (int i = 0; i < inlineFormulas.length; i++) {
          html = html.replaceAll(
              '|||INLINE_FORMULA_${i}|||', '\\(${inlineFormulas[i]}\\)');
        }

        // 替换图片路径
        final (processedHtml, imagePaths) = await replaceHtmlBase64Img(
            html, mediaDir ?? PathUtils(docPath).parent);
        note.fields[fieldId] = processedHtml.trim();
        note.mediaList.addAll(imagePaths);
      } catch (e) {
        logger.e("convertMD2HTML error: $e");
      }
    }
    return note;
  }

  Future<List<Map<String, String>>> splitMarkdownByLevel(
      String docPath, int level) async {
    // 读取文件内容
    final content = await readFileWithFallback(docPath);
    if (content.isEmpty) {
      return [];
    }

    // 用于存储结果
    final List<Map<String, String>> result = [];

    // 分割文本为行
    final lines = content.split('\n');

    // 标题模式的正则表达式
    final headingRegexes = [
      RegExp(r'^# '), // h1
      RegExp(r'^## '), // h2
      RegExp(r'^### '), // h3
      RegExp(r'^#### '), // h4
      RegExp(r'^##### '), // h5
      RegExp(r'^###### '), // h6
    ];

    // 标题级别验证 - 确保level值有效
    if (level < 1 || level > 6) {
      logger.w("无效的level值: $level，应为1-6");
      return [];
    }

    // 根节点(虚拟)
    final rootNode = DocNode(0, "", -1);
    // 当前节点栈，用于跟踪层级关系
    final nodeStack = <DocNode>[rootNode];
    // 所有节点列表，按顺序存储
    final allNodes = <DocNode>[];

    // 第一步：单次遍历构建文档树和收集内容
    for (int i = 0; i < lines.length; i++) {
      final line = lines[i];

      // 检查是否是标题行
      int headingLevel = -1;
      for (int j = 0; j < headingRegexes.length; j++) {
        if (headingRegexes[j].hasMatch(line)) {
          headingLevel = j + 1;
          break;
        }
      }

      if (headingLevel > 0) {
        // 创建新节点
        final newNode = DocNode(headingLevel, line, i);
        allNodes.add(newNode);

        // 更新节点栈，保持正确的层级关系
        while (nodeStack.length > 1 && nodeStack.last.level >= headingLevel) {
          nodeStack.removeLast();
        }

        // 添加到父节点的子节点列表
        if (nodeStack.isNotEmpty) {
          nodeStack.last.children.add(newNode);
        }
        // 当前节点入栈
        nodeStack.add(newNode);
      } else if (nodeStack.isNotEmpty) {
        // 收集标题后的内容
        nodeStack.last.contentAfter.add(line);
      }
    }

    // 第二步：标记叶子节点和需要处理的节点
    void markNodesForProcessing(DocNode node, bool parentHasTargetLevel) {
      if (node.level == 0) {
        // 根节点特殊处理
        for (var child in node.children) {
          markNodesForProcessing(child, false);
        }
        return;
      }

      // 检查当前节点是否是叶子节点（在目标级别上下文中）
      bool hasChildrenAtTargetLevel = false;
      for (var child in node.children) {
        if (child.level <= level) {
          hasChildrenAtTargetLevel = true;
          break;
        }
      }

      // 更新叶子节点状态
      node.isLeafNode = !hasChildrenAtTargetLevel;

      // 设置是否处理该节点
      bool isTargetLevel = node.level == level;
      bool isLeafBelowTarget = node.level < level && node.isLeafNode;
      node.processForLevel = isTargetLevel || isLeafBelowTarget;

      // 递归处理子节点
      for (var child in node.children) {
        markNodesForProcessing(
            child, node.processForLevel || parentHasTargetLevel);
      }
    }

    // 从根节点开始标记
    markNodesForProcessing(rootNode, false);

    // 用于构建标题路径的辅助函数
    String buildTitlePath(DocNode node) {
      // 构建从根到当前节点的路径
      final pathNodes = <DocNode>[];
      DocNode? current = node;
      while (current != null && current.level > 0) {
        pathNodes.insert(0, current);

        // 查找父节点
        DocNode? parent;
        for (var n in allNodes) {
          if (n.position < current.position &&
              n.level < current.level &&
              (parent == null || n.position > parent.position)) {
            parent = n;
          }
        }

        current = parent;
      }

      // 如果没有找到任何路径节点，返回空
      if (pathNodes.isEmpty) {
        return "";
      }

      // 构建标题文本，包含父标题及其内容
      final titleBuilder = StringBuffer();
      for (int i = 0; i < pathNodes.length; i++) {
        final pathNode = pathNodes[i];

        // 添加标题行
        titleBuilder.write(pathNode.line);
        titleBuilder.write('\n');

        // 对于非最后一个节点，添加其内容
        if (i < pathNodes.length - 1) {
          final contentAfter = pathNode.contentAfter;
          if (contentAfter.isNotEmpty) {
            titleBuilder.write('\n');
            titleBuilder.write(contentAfter.join('\n'));
            titleBuilder.write('\n\n');
          } else {
            titleBuilder.write('\n');
          }
        }
      }

      return titleBuilder.toString().trim();
    }

    // 第三步：处理基于节点层级的片段
    for (var node in allNodes) {
      if (node.processForLevel) {
        // 标题路径
        final titlePath = buildTitlePath(node);
        if (titlePath.isEmpty) {
          // 跳过空标题路径
          continue;
        }

        // 收集当前节点的内容和高于目标级别的子节点内容
        final allBodyContent = <String>[];

        // 首先添加当前节点自己的内容
        if (node.contentAfter.isNotEmpty) {
          allBodyContent.addAll(node.contentAfter);
        }

        // 递归收集子节点内容（如果子节点级别高于目标级别）
        void collectChildContent(DocNode childNode) {
          if (childNode.level > level) {
            // 添加子节点标题
            allBodyContent.add(childNode.line);
            // 添加子节点内容
            if (childNode.contentAfter.isNotEmpty) {
              allBodyContent.addAll(childNode.contentAfter);
            }
            // 递归处理子节点的子节点
            for (var grandChild in childNode.children) {
              collectChildContent(grandChild);
            }
          }
        }

        // 对所有子节点收集内容
        for (var child in node.children) {
          collectChildContent(child);
        }

        // 添加到结果（如果body不为空）
        final bodyContent = allBodyContent.join('\n').trim();
        if (bodyContent.isNotEmpty) {
          result.add({'title': titlePath, 'body': bodyContent});
        }
      }
    }

    return result;
  }

  /// 生成并导入Anki卡片
  Future<void> generateAndImportCards(List<AnkiNote> notes,
      {List<String> mediaList = const [],
      bool isDelete = true,
      List<String> internalMediaTypes = const []}) async {
    if (notes.isEmpty) {
      throw Exception("未识别到卡片，请检查文件规范性和制卡参数");
    }
    final settingController = Get.find<SettingController>();
    final progressController = Get.find<ProgressController>();
    final outputPath = await PathUtils.getOutputApkgPath();
    final resp = await genApkg(notes, mediaList, outputPath,
        internalMediaTypes: internalMediaTypes);
    logger.i("generateAndImportCards resp: $resp");
    if (resp.status == "success") {
      final exportMode = settingController.cardMode.value;
      if (exportMode == "ankiconnect") {
        final importResp = await importApkg(
          resp.data,
          isDelete: isDelete,
        );
        if (importResp.data['error'] == null) {
          progressController.updateProgress(status: "completed");
          progressController.outputPath.value = resp.data;
        } else {
          progressController.updateProgress(
            status: "error",
            message: importResp.data['error'],
          );
        }
      } else {
        progressController.outputPath.value = resp.data;
        progressController.updateProgress(status: "completed");
      }
    } else {
      progressController.updateProgress(status: "error", message: resp.message);
    }
  }

  Future<RustResponse> simulatePaste() async {
    if (Platform.isMacOS) {
      const platform = MethodChannel('accessibility_permission');
      final resp = await platform.invokeMethod('checkAccessibilityPermission');
      logger.i('checkAccessibilityPermission resp: $resp');
      if (resp == true) {
        final resp2 = await platform.invokeMethod('simulatePaste');
        logger.i('simulatePaste resp: $resp2');
        return RustResponse(
            interactionId: "",
            status: "success",
            data: resp2.toString(),
            message: 'common.completed'.tr);
      } else {
        return RustResponse(
            interactionId: "", status: "error", data: "", message: "无权限");
      }
    } else if (Platform.isWindows) {
      String appPath = Platform.resolvedExecutable;
      String seekPath = PathUtils.join([PathUtils(appPath).parent, 'seek.exe']);
      await Process.run(seekPath, ['paste']);
    }
    return RustResponse(
        interactionId: "", status: "error", data: "", message: "不支持的平台");
  }

  /// 创建新牌组
  /// [deckName] - 要创建的牌组名称
  /// 返回操作结果
  Future<dynamic> createDeck(String deckName) async {
    final dio = Dio();
    final settingController = Get.find<SettingController>();
    final address = settingController.ankiConnectUrl.value;
    final response = await dio.post(address, data: {
      "action": "createDeck",
      "version": 6,
      "params": {"deck": deckName}
    });
    return response;
  }

  /// 删除牌组
  /// [deckNames] - 要删除的牌组名称列表
  /// [cardsToo] - 是否同时删除牌组中的卡片
  /// 返回操作结果
  Future<dynamic> deleteDecks(List<String> deckNames,
      {bool cardsToo = true}) async {
    final dio = Dio();
    final settingController = Get.find<SettingController>();
    final address = settingController.ankiConnectUrl.value;
    final response = await dio.post(address, data: {
      "action": "deleteDecks",
      "version": 6,
      "params": {"decks": deckNames, "cardsToo": cardsToo}
    });
    return response;
  }

  /// 克隆牌组
  /// [sourceDeck] - 源牌组名称
  /// [targetDeck] - 目标牌组名称
  /// [batchSize] - 每批处理的卡片数量，默认为100
  /// 返回操作结果
  Future<dynamic> cloneDeck(String sourceDeck, String targetDeck,
      {int batchSize = 100}) async {
    try {
      final dio = Dio();
      final settingController = Get.find<SettingController>();

      // 1. 获取所有牌组名称
      final allDecks = await getDeckNames();

      // 2. 筛选出源牌组及其所有子牌组
      final sourceDecks = allDecks
          .where(
              (deck) => deck == sourceDeck || deck.startsWith('$sourceDeck::'))
          .toList();

      // 3. 创建目标牌组及其子牌组
      for (var deck in sourceDecks) {
        // 计算目标牌组名称（保持层级结构）
        final targetDeckName = deck == sourceDeck
            ? targetDeck
            : deck.replaceFirst(sourceDeck, targetDeck);

        // 创建牌组
        await createDeck(targetDeckName);

        // 获取当前牌组中的所有笔记
        final notes = await findNotes('deck:"$deck"');

        // 获取每个笔记的详细信息
        final notesInfo =
            await getNotesInfo(notes.map((n) => n['noteId'] as int).toList());
        logger.i("notesInfo: $notesInfo");
        // 批量处理笔记
        for (var i = 0; i < notesInfo.length; i += batchSize) {
          final batch = notesInfo.skip(i).take(batchSize).toList();

          // 准备批量添加的笔记
          final notesToAdd = batch.map((note) {
            // 转换fields格式
            final fields = <String, String>{};
            final originalFields = note['fields'] as Map<String, dynamic>;
            originalFields.forEach((key, value) {
              fields[key] = value['value'] as String;
            });

            return {
              "deckName": targetDeckName,
              "modelName": note['modelName'],
              "fields": fields,
              "tags": note['tags'],
              "options": {
                "allowDuplicate": false,
                "duplicateScope": "deck",
                "duplicateScopeOptions": {
                  "deckName": targetDeckName,
                  "checkChildren": false,
                  "checkAllModels": false
                }
              }
            };
          }).toList();

          logger.i("notesToAdd: $notesToAdd");
          // 批量添加笔记
          await dio.post(settingController.ankiConnectUrl.value, data: {
            "action": "addNotes",
            "version": 6,
            "params": {"notes": notesToAdd}
          });
        }
      }
      return {"result": true, "error": null};
    } catch (e) {
      logger.e("cloneDeck error: $e");
      return {"result": false, "error": e.toString()};
    }
  }

  /// 删除空牌组
  /// [deleteRange] - 删除范围，可选值：'all'（所有空牌组）或 'part'（指定父牌组下的空牌组）
  /// [parentDeck] - 当 deleteRange 为 'part' 时，指定要删除空牌组的父牌组
  /// 返回操作结果
  Future<dynamic> deleteEmptyDecks(String deleteRange,
      {String? parentDeck}) async {
    try {
      // 1. 获取所有牌组名称
      final allDecks = await getDeckNames();
      logger.i("allDecks: $allDecks");

      // 2. 筛选出空牌组
      final emptyDecks = <String>[];
      final progressController = Get.find<ProgressController>();
      final dio = Dio();
      final settingController = Get.find<SettingController>();

      for (var i = 0; i < allDecks.length; i++) {
        final deck = allDecks[i];
        // 更新进度
        progressController.updateProgress(
          status: "running",
          message: "正在检查牌组: $deck",
          current: i.toDouble(),
          total: allDecks.length.toDouble(),
        );

        // 使用findNotes查询牌组中的卡片数量
        final response =
            await dio.post(settingController.ankiConnectUrl.value, data: {
          "action": "findNotes",
          "version": 6,
          "params": {"query": 'deck:"$deck"'}
        });

        if (response.data['error'] != null) {
          throw Exception(response.data['error']);
        }

        final noteIds = response.data['result'] as List<dynamic>;
        final totalCards = noteIds.length;
        logger.i("deck: $deck, totalCards: $totalCards");

        // 根据删除范围筛选
        if (totalCards == 0) {
          if (deleteRange == 'all') {
            emptyDecks.add(deck);
          } else if (deleteRange == 'part' &&
              parentDeck != null &&
              (deck == parentDeck || deck.startsWith('$parentDeck::'))) {
            emptyDecks.add(deck);
          }
        }
      }

      // 3. 如果没有空牌组，直接返回
      if (emptyDecks.isEmpty) {
        return {"result": true, "message": "没有找到空牌组"};
      }

      logger.i("emptyDecks: $emptyDecks");
      // 4. 删除空牌组
      final deleteResponse = await deleteDecks(emptyDecks);

      return {
        "result": true,
        "message": "成功删除 ${emptyDecks.length} 个空牌组",
        "deletedDecks": emptyDecks
      };
    } catch (e) {
      logger.e("deleteEmptyDecks error: $e");
      return {"result": false, "error": e.toString()};
    }
  }

  Future<dynamic> exportCSV(String deckName, String path) async {
    try {
      // 先获取JSON数据
      final jsonResult =
          await exportJson(deckName, await PathUtils.getTempFilePath(".json"));
      if (jsonResult['result'] != true) {
        return jsonResult;
      }

      final List<dynamic> rawCards =
          jsonDecode(await File(jsonResult['path']).readAsString());
      final List<Map<String, dynamic>> cards =
          rawCards.map((card) => Map<String, dynamic>.from(card)).toList();

      if (cards.isEmpty) {
        return {'result': false, 'error': 'No cards found'};
      }

      // 获取所有可能的字段名
      final Set<String> allFields = <String>{};
      for (var card in cards) {
        allFields.addAll(card.keys);
      }
      final List<String> headers = allFields.toList();

      // 创建CSV数据
      final StringBuffer csvData = StringBuffer();

      // 写入表头
      csvData.writeln(
          headers.map((h) => '"${h.replaceAll('"', '""')}"').join(','));

      // 写入数据行
      for (var card in cards) {
        final List<String> row = headers.map((header) {
          final value = card[header]?.toString() ?? '';
          // 处理包含逗号、换行或双引号的值
          if (value.contains(RegExp(r'[,\n"]'))) {
            return '"${value.replaceAll('"', '""')}"';
          }
          return value;
        }).toList();
        csvData.writeln(row.join(','));
      }

      // 写入文件
      await File(path).writeAsString(csvData.toString());

      // 删除临时JSON文件
      await File(jsonResult['path']).delete();

      return {'result': true, 'count': cards.length, 'path': path};
    } catch (e, stack) {
      logger.e("Export CSV error: $e\n$stack");
      return {'result': false, 'error': e.toString()};
    }
  }

  Future<dynamic> exportXlsx(String deckName, String path) async {
    try {
      // 先获取JSON数据
      final jsonResult =
          await exportJson(deckName, await PathUtils.getTempFilePath(".json"));
      if (jsonResult['result'] != true) {
        return jsonResult;
      }

      final List<dynamic> rawCards =
          jsonDecode(await File(jsonResult['path']).readAsString());
      final List<Map<String, dynamic>> cards =
          rawCards.map((card) => Map<String, dynamic>.from(card)).toList();

      if (cards.isEmpty) {
        return {'result': false, 'error': 'No cards found'};
      }

      // 获取所有可能的字段名
      final Set<String> allFields = <String>{};
      for (var card in cards) {
        allFields.addAll(card.keys);
      }
      final List<String> headers = allFields.toList();

      // 创建Excel工作簿和工作表
      final excel = Excel.createExcel();
      final sheet = excel['Sheet1'];

      // 写入表头
      for (var i = 0; i < headers.length; i++) {
        final cell =
            sheet.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0));
        cell.value = TextCellValue(headers[i]);
        cell.cellStyle = CellStyle(
          bold: true,
          horizontalAlign: HorizontalAlign.Center,
        );
      }

      // 写入数据
      for (var rowIndex = 0; rowIndex < cards.length; rowIndex++) {
        final card = cards[rowIndex];
        for (var colIndex = 0; colIndex < headers.length; colIndex++) {
          final value = card[headers[colIndex]]?.toString() ?? '';
          sheet
              .cell(CellIndex.indexByColumnRow(
                columnIndex: colIndex,
                rowIndex: rowIndex + 1,
              ))
              .value = TextCellValue(value);
        }
      }

      // 保存文件
      final List<int>? fileBytes = excel.save();
      if (fileBytes != null) {
        File(path)
          ..createSync(recursive: true)
          ..writeAsBytesSync(fileBytes);
      }

      // 删除临时JSON文件
      await File(jsonResult['path']).delete();

      return {'result': true, 'count': cards.length, 'path': path};
    } catch (e, stack) {
      logger.e("Export XLSX error: $e\n$stack");
      return {'result': false, 'error': e.toString()};
    }
  }
}

/// 内部类,用于存储挖空匹配信息
class _ClozeMatch {
  final String fullMatch;
  final String? groupNum;
  final String content;
  final int start;
  final bool isStandard;

  _ClozeMatch({
    required this.fullMatch,
    this.groupNum,
    required this.content,
    required this.start,
    required this.isStandard,
  });
}

/// 处理HTML中的图片路径，返回处理后的HTML和图片路径列表
Future<(String, List<String>)> replaceHtmlBase64Img(
    String html, String basePath,
    {String htmlType = ""}) async {
  final messageController = Get.find<MessageController>();
  final tempDir = await PathUtils.tempDir;
  final data = {
    'html': html,
    'base_path': basePath,
    'temp_dir': tempDir,
    'html_type': htmlType,
    'show_progress': false,
  };

  final resp = await messageController.request(data, 'html/replace_base64_img');
  if (resp.status == 'success') {
    final result = jsonDecode(resp.data);
    return (
      result['html'] as String,
      (result['image_paths'] as List).cast<String>(),
    );
  }

  throw Exception(resp.message);
}

/// 处理HTML中的图片路径，返回处理后的HTML
Future<String> replaceHtmlLocalImg(String html, String basePath,
    {String htmlType = ""}) async {
  final messageController = Get.find<MessageController>();
  final tempDir = await PathUtils.tempDir;
  final data = {
    'html': html,
    'base_path': basePath,
    'temp_dir': tempDir,
    'html_type': htmlType,
    'show_progress': false,
  };

  final resp = await messageController.request(data, 'html/replace_local_img');
  if (resp.status == 'success') {
    final result = jsonDecode(resp.data);
    return result['html'] as String;
  }

  throw Exception(resp.message);
}

class PDFUtils {
  static Future<String> extractPages(
      String filePath, String pageRange, String outputPath,
      {bool showProgress = true}) async {
    final PdfDocument document = PdfDocument(
      inputBytes: File(filePath).readAsBytesSync(),
    );
    final List<int> pages = parsePageRange(pageRange, document.pages.count);
    // 收集要删除的页码
    final List<int> pagesToDelete = [];
    for (int i = 0; i < document.pages.count; i++) {
      if (!pages.contains(i)) {
        pagesToDelete.add(i);
      }
    }
    logger.i("pagesToDelete: ${pagesToDelete}");
    // 倒序删除页面
    for (int i = pagesToDelete.length - 1; i >= 0; i--) {
      PdfPage page = document.pages[pagesToDelete[i]];
      document.pages.remove(page);
    }
    // Save the document
    File(outputPath).writeAsBytesSync(await document.save());
    document.dispose();
    return outputPath;
  }
}
