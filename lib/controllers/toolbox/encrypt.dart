import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:syncfusion_flutter_pdf/pdf.dart';

class PDFEncryptPageController extends GetxController {
  // 基本数据
  final encryptModeList = [
    {'value': 'encrypt', 'label': 'toolbox.encrypt.mode.encrypt'.tr},
    {'value': 'decrypt', 'label': 'toolbox.encrypt.mode.decrypt'.tr},
  ];
  final encryptTypeList = [
    {'value': 'user', 'label': 'toolbox.encrypt.type.user'.tr},
    {'value': 'permission', 'label': 'toolbox.encrypt.type.permission'.tr},
  ];
  final encryptPermissionList = [
    {'value': 'print', 'label': 'toolbox.encrypt.permission.print'.tr},
    {
      'value': 'copyContent',
      'label': 'toolbox.encrypt.permission.copyContent'.tr
    },
    {
      'value': 'editContent',
      'label': 'toolbox.encrypt.permission.editContent'.tr
    },
    {
      'value': 'editAnnotations',
      'label': 'toolbox.encrypt.permission.editAnnotations'.tr
    },
    {
      'value': 'fillFields',
      'label': 'toolbox.encrypt.permission.fillFields'.tr
    },
    {
      'value': 'assembleDocument',
      'label': 'toolbox.encrypt.permission.assembleDocument'.tr
    },
  ];

  // 表单参数
  final encryptTypes = <String>['user'].obs;
  final userPassword = ''.obs;
  final permissionPassword = ''.obs;
  final confirmUserPassword = ''.obs;
  final confirmPermissionPassword = ''.obs;
  final encryptPermissions = <String>['print', 'copyContent'].obs;
  final pageRange = ''.obs;
  final outputMode = "same".obs;
  final outputDir = ''.obs;
  final selectedFilePaths = <String>[].obs;

  // 错误信息
  final userPasswordError = ''.obs;
  final permissionPasswordError = ''.obs;
  final outputDirError = ''.obs;

  // 控制器
  final progressController = Get.find<ProgressController>();
  final messageController = Get.find<MessageController>();
  final settingController = Get.find<SettingController>();
  final tabController = ShadTabsController(value: 'encrypt');

  // 验证密码
  bool validatePasswords() {
    bool isValid = true;

    // 加密模式下需要验证密码
    if (tabController.selected == 'encrypt') {
      // 验证用户密码
      if (encryptTypes.contains('user')) {
        if (userPassword.isEmpty) {
          userPasswordError.value = 'toolbox.encrypt.passwordError.required'.tr;
          isValid = false;
        } else if (userPassword.value != confirmUserPassword.value) {
          userPasswordError.value = 'toolbox.encrypt.passwordError.mismatch'.tr;
          isValid = false;
        } else {
          userPasswordError.value = '';
        }
      }

      // 验证权限密码
      if (encryptTypes.contains('permission')) {
        if (permissionPassword.isEmpty) {
          permissionPasswordError.value =
              'toolbox.encrypt.passwordError.permissionRequired'.tr;
          isValid = false;
        } else if (permissionPassword.value !=
            confirmPermissionPassword.value) {
          permissionPasswordError.value =
              'toolbox.encrypt.passwordError.permissionMismatch'.tr;
          isValid = false;
        } else {
          permissionPasswordError.value = '';
        }
      }
    }

    return isValid;
  }

  Future<void> submit(BuildContext context) async {
    if (selectedFilePaths.isEmpty) {
      showToastNotification(
        context,
        'toolbox.common.failure'.tr,
        'toolbox.common.selectPdfFiles'.tr,
        type: "error",
      );
      return;
    }
    progressController.reset(
      showOutputHint: true,
      numberButtons: 2,
    );
    progressController.showProgressDialog(context);
    try {
      // 验证密码
      if (!validatePasswords()) {
        progressController.updateProgress(
          status: "error", message: 'toolbox.encrypt.passwordError.validation'.tr,
        );
        return;
      }

      // 处理每个选中的PDF文件
      for (String filePath in selectedFilePaths) {
        try {
          // 确定输出路径
          final pathUtils = PathUtils(filePath);
          String outputPath = await pathUtils.convertPath(
            outputMode.value,
            stem_append:
                "_${tabController.selected == 'encrypt' ? 'toolbox.encrypt.fileNameAppend.encrypt'.tr : 'toolbox.encrypt.fileNameAppend.decrypt'.tr}",
            outputDir: outputDir.value,
          );

          // 加载PDF文档
          PdfDocument document;
          if (tabController.selected == 'decrypt') {
            // 解密模式需要提供密码打开文档
            document = PdfDocument(
              inputBytes: File(filePath).readAsBytesSync(),
              password: userPassword.value,
            );
            // 移除密码保护
            document.security.userPassword = '';
            document.security.ownerPassword = '';
          } else {
            // 加密模式直接打开文档
            document = PdfDocument(
              inputBytes: File(filePath).readAsBytesSync(),
            );

            // 设置加密算法
            document.security.algorithm = PdfEncryptionAlgorithm.aesx256Bit;

            // 根据选择的加密类型设置密码
            if (encryptTypes.contains('user')) {
              document.security.userPassword = userPassword.value;
            }
            if (encryptTypes.contains('permission')) {
              document.security.ownerPassword = permissionPassword.value;
              // 设置权限
              if (encryptPermissions.contains('print')) {
                document.security.permissions.remove(PdfPermissionsFlags.print);
                document.security.permissions
                    .remove(PdfPermissionsFlags.fullQualityPrint);
              }
              if (encryptPermissions.contains('editContent')) {
                document.security.permissions
                    .remove(PdfPermissionsFlags.editContent);
              }
              if (encryptPermissions.contains('copyContent')) {
                document.security.permissions
                    .remove(PdfPermissionsFlags.copyContent);
                document.security.permissions
                    .remove(PdfPermissionsFlags.accessibilityCopyContent);
              }
              if (encryptPermissions.contains('editAnnotations')) {
                document.security.permissions
                    .remove(PdfPermissionsFlags.editAnnotations);
              }
              if (encryptPermissions.contains('fillFields')) {
                document.security.permissions
                    .remove(PdfPermissionsFlags.fillFields);
              }
              if (encryptPermissions.contains('assembleDocument')) {
                document.security.permissions
                    .remove(PdfPermissionsFlags.assembleDocument);
              }
            }
          }

          // 保存加密/解密后的文档
          await File(outputPath).writeAsBytes(await document.save());
          document.dispose();

          progressController.outputPath.value = outputPath;
          progressController.updateProgress(
            status: "running", message: "${'toolbox.common.process.running'.tr}: ${PathUtils(filePath).name}",
          );
        } catch (e) {
          logger.e("Error processing file $filePath: $e");
          progressController.updateProgress(
            status: "error", message: "toolbox.common.error_with_msg".trParams({'error': e.toString()}),
          );
          return;
        }
      }

      progressController.updateProgress(status: "completed", message: 'common.completed'.tr);
    } catch (e) {
      logger.e("submit error: $e");
      progressController.updateProgress(
        status: "error", message: "toolbox.common.error_with_msg".trParams({'error': e.toString()}),
      );
    }
  }
}
