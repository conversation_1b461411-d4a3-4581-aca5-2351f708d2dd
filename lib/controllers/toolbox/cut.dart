import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/controllers/common.dart';

class PDFCutPageController extends GetxController {
  // 基础数据
  late final List<Map<String, String>> cutModeList;
  late final List<Map<String, String>> orientationList;

  // 表单参数
  final cutMode = "grid".obs;
  final numCols = 2.obs;
  final numRows = 1.obs;
  final pageSize = "A4".obs;
  final orientation = "portrait".obs;
  final top = 10.0.obs;
  final bottom = 10.0.obs;
  final left = 10.0.obs;
  final right = 10.0.obs;
  final hBreakpoints = "".obs;
  final vBreakpoints = "".obs;
  final pageRange = ''.obs;
  final outputMode = "same".obs;
  final outputPath = ''.obs;
  final outputDir = ''.obs;
  final selectedFilePaths = <String>[].obs;
  // 错误
  final outputDirError = ''.obs;
  // 控制器
  final progressController = Get.find<ProgressController>();
  final messageController = Get.find<MessageController>();
  final settingController = Get.find<SettingController>();
  final tabController = ShadTabsController(value: 'annotate');

  @override
  void onInit() {
    super.onInit();

    // Initialize lists with i18n translations
    cutModeList = [
      {"value": "grid", "label": 'toolbox.cut.cutOptions.grid'.tr},
      {"value": "page", "label": 'toolbox.cut.cutOptions.page'.tr},
      {"value": "custom", "label": 'toolbox.cut.cutOptions.custom'.tr},
    ];

    orientationList = [
      {"value": "portrait", "label": 'toolbox.combine.portrait'.tr},
      {"value": "landscape", "label": 'toolbox.combine.landscape'.tr},
    ];
  }

  Future<void> submit(BuildContext context) async {
    if (selectedFilePaths.isEmpty) {
      showToastNotification(
        context,
        'toolbox.common.failure'.tr,
        'toolbox.common.selectPdfFiles'.tr,
        type: "error",
      );
      return;
    }
    progressController.reset(
      showOutputHint: true,
      numberButtons: 2,
    );
    progressController.showProgressDialog(context);
    try {
      for (String filePath in selectedFilePaths) {
        progressController.updateProgress(
          status: "running",
          message: "${'toolbox.background.processingFile'.tr}: ${PathUtils(filePath).name}",
        );
        final outputPath = await PathUtils(filePath).convertPath(
          outputMode.value,
          stem_append: "_${'toolbox.cut.title'.tr}",
          suffix: ".pdf",
          outputDir: outputDir.value,
        );
        List<double> hBreakpointsList = [];
        List<double> vBreakpointsList = [];
        if (hBreakpoints.value.isNotEmpty) {
          try {
            hBreakpointsList =
                hBreakpoints.value.split(',').map(double.parse).toList();
          } catch (e) {
            showToastNotification(
              context,
              'toolbox.common.failure'.tr,
              'toolbox.cut.custom.hBreakpointsInvalid'.tr,
              type: "error",
            );
            return;
          }
        }
        if (vBreakpoints.value.isNotEmpty) {
          try {
            vBreakpointsList =
                vBreakpoints.value.split(',').map(double.parse).toList();
          } catch (e) {
            showToastNotification(
              context,
              'toolbox.common.failure'.tr,
              'toolbox.cut.custom.vBreakpointsInvalid'.tr,
              type: "error",
            );
            return;
          }
        }
        final data = {
          'cut_type': cutMode.value,
          'num_cols': numCols.value,
          'num_rows': numRows.value,
          'paper_size': pageSize.value,
          'orientation': orientation.value,
          'top': top.value,
          'bottom': bottom.value,
          'left': left.value,
          'right': right.value,
          'h_breakpoints': hBreakpointsList,
          'v_breakpoints': vBreakpointsList,
          'input_path': filePath,
          'page_range': pageRange.value,
          'output_path': outputPath,
          'show_progress': true,
        };
        final resp = await messageController.request(data, 'pdf/cut');
        logger.w("resp: $resp");
        if (resp.status == "success") {
          progressController.outputPath.value = outputPath;
          progressController.updateProgress(
            status: "completed", message: 'toolbox.background.completed'.tr,
          );
        } else {
          progressController.updateProgress(
              status: "error", message: resp.message);
          return;
        }
      }
      progressController.updateProgress(status: "completed", message: 'common.completed'.tr);
    } catch (e) {
      logger.e("submit error: $e");
      progressController.updateProgress(
        status: "error", message: "${'toolbox.common.operationFailed'.tr}: $e",
      );
    }
  }
}
