import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/common.dart';
import 'dart:convert';
import 'package:syncfusion_flutter_pdf/pdf.dart';

class PDFRotatePageController extends GetxController {
  // 基本数据
  final rotationList = [
    {'value': '90', 'label': '90°'},
    {'value': '180', 'label': '180°'},
    {'value': '270', 'label': '270°'},
  ];
  // 表单参数
  final rotation = '90'.obs;
  final pageRange = ''.obs;
  final outputMode = "same".obs;
  final outputDir = ''.obs;
  final selectedFilePaths = <String>[].obs;
  // 错误
  final outputDirError = ''.obs;
  // 控制器
  final progressController = Get.find<ProgressController>();
  final messageController = Get.find<MessageController>();
  final settingController = Get.find<SettingController>();

  void submit(BuildContext context) async {
    if (selectedFilePaths.isEmpty) {
      showToastNotification(
        context,
        "失败",
        "请选择PDF文件",
        type: "error",
      );
      return;
    }
    progressController.reset(
      showOutputHint: true,
      numberButtons: 2,
    );
    progressController.showProgressDialog(context);

    try {
      final int angle = int.tryParse(rotation.value) ?? 0;
      // Process each selected PDF file
      for (String filePath in selectedFilePaths) {
        try {
          // Load the PDF document
          final PdfDocument document = PdfDocument(
            inputBytes: File(filePath).readAsBytesSync(),
          );
          // Get page range or process all pages
          final List<int> pages =
              parsePageRange(pageRange.value, document.pages.count);
          // Rotate specified pages
          for (int pageNum in pages) {
            if (pageNum <= document.pages.count) {
              var currentAngle = 0;
              if (document.pages[pageNum].rotation ==
                  PdfPageRotateAngle.rotateAngle0) {
                currentAngle = 0;
              } else if (document.pages[pageNum].rotation ==
                  PdfPageRotateAngle.rotateAngle90) {
                currentAngle = 90;
              } else if (document.pages[pageNum].rotation ==
                  PdfPageRotateAngle.rotateAngle180) {
                currentAngle = 180;
              } else if (document.pages[pageNum].rotation ==
                  PdfPageRotateAngle.rotateAngle270) {
                currentAngle = 270;
              }
              final newAngle = (currentAngle + angle) % 360;
              // logger.i("currentAngle: $currentAngle, newAngle: $newAngle");
              // Convert from 1-based page number to 0-based index
              if (newAngle == 90) {
                document.pages[pageNum].rotation =
                    PdfPageRotateAngle.rotateAngle90;
              } else if (newAngle == 180) {
                document.pages[pageNum].rotation =
                    PdfPageRotateAngle.rotateAngle180;
              } else if (newAngle == 270) {
                document.pages[pageNum].rotation =
                    PdfPageRotateAngle.rotateAngle270;
              } else {
                document.pages[pageNum].rotation =
                    PdfPageRotateAngle.rotateAngle0;
              }
            }
          }

          // Determine output path
          final pathUtils = PathUtils(filePath);
          String outputPath = await pathUtils.convertPath(
            outputMode.value,
            stem_append: "_rotated",
            outputDir: outputDir.value,
          );

          // Save the document
          File(outputPath).writeAsBytesSync(await document.save());
          document.dispose();

          progressController.outputPath.value = outputPath;
          progressController.updateProgress(
            status: "running",
            message: "${'toolbox.common.process.running'.tr}: ${PathUtils(filePath).name}",
          );
        } catch (e) {
          logger.e("Error processing file $filePath: $e");
          progressController.updateProgress(
            status: "error", message: "${'toolbox.common.process.processFailed'.tr}: $e",
          );
          return;
        }
      }

      progressController.updateProgress(status: "completed", message: 'common.completed'.tr);
    } catch (e) {
      logger.e("submit error: $e");
      progressController.updateProgress(
        status: "error", message: "${'toolbox.common.process.failed'.tr}: $e",
      );
    }
  }
}
