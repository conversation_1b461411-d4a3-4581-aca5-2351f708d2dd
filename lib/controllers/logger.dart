import 'dart:io';
import 'package:anki_guru/controllers/utils.dart';
import 'package:logger/logger.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as p;

class LoggerService {
  static final LoggerService _instance = LoggerService._internal();
  static late Logger logger;
  static File? _logFile;

  factory LoggerService() {
    return _instance;
  }

  LoggerService._internal();

  static Future<void> init() async {
    // Add a small delay to ensure Flutter engine is fully initialized
    await Future.delayed(const Duration(milliseconds: 100));

    var directory = "";
    bool useFileLogging = true;

    if (Platform.isAndroid) {
      // Try to get external storage directory with fallback options
      try {
        directory = (await getExternalStorageDirectory())?.path ?? '';
        print("Android: getExternalStorageDirectory succeeded: $directory");
      } catch (e) {
        print("Android: getExternalStorageDirectory failed: $e");
        useFileLogging = false;
      }
    } else if (Platform.isIOS) {
      try {
        directory = (await getApplicationSupportDirectory()).path;
      } catch (e) {
        useFileLogging = false;
        print("Warning: Failed to get iOS application support directory, disabling file logging: $e");
      }
    } else if (Platform.isMacOS) {
      directory = p.join(Platform.environment['HOME'] ?? '', '.pdf_guru');
    } else if (Platform.isWindows) {
      directory =
          p.join(Platform.environment['USERPROFILE'] ?? '', '.pdf_guru');
    } else if (Platform.isLinux) {
      directory = p.join(Platform.environment['HOME'] ?? '', '.pdf_guru');
    }

    // Only set up file logging if we have a valid directory
    if (useFileLogging && directory.isNotEmpty) {
      try {
        _logFile = File(PathUtils.join([directory, 'app.log']));

        // Check if log file exists and its size
        if (await _logFile!.exists()) {
          final fileSize = await _logFile!.length();
          const maxSizeBytes = 50 * 1024 * 1024; // 50 MB in bytes

          if (fileSize > maxSizeBytes) {
            print("Log file size ($fileSize bytes) exceeds 50 MB limit. Deleting and creating new log file.");
            await _logFile!.delete();
            await _logFile!.create(recursive: true);
          }
        } else {
          await _logFile!.create(recursive: true);
        }

        print("Logger initialized with file: ${_logFile!.path}");
      } catch (e) {
        print("Warning: Failed to create log file, disabling file logging: $e");
        _logFile = null;
      }
    } else {
      _logFile = null;
      print("Logger initialized without file logging");
    }

    // Create logger with appropriate output based on file availability
    if (_logFile != null) {
      // File logging available - use both console and file output
      logger = Logger(
        filter: MyFilter(),
        output: MultiOutput([
          ConsoleOutput(),
          FileOutput(file: _logFile!),
        ]),
        printer: PrettyPrinter(
          methodCount: 2,
          errorMethodCount: 8,
          lineLength: 120,
          colors: true,
          printEmojis: true,
        ),
        level: Level.all,
      );
    } else {
      // File logging not available - use console output only
      logger = Logger(
        filter: MyFilter(),
        output: ConsoleOutput(),
        printer: PrettyPrinter(
          methodCount: 2,
          errorMethodCount: 8,
          lineLength: 120,
          colors: true,
          printEmojis: true,
        ),
        level: Level.all,
      );
    }

    logger.i("Logger initialized at ${DateTime.now()}");
    logger.d("Log file path: ${_logFile!.path}");
  }

  /// 关闭日志系统，确保所有日志都被写入文件
  static Future<void> close() async {
    // 写入最后一条日志
    logger.i("Logger closed at ${DateTime.now()}");

    // 刷新文件缓冲区
    try {
      // 对于某些日志实现，可能需要显式关闭
      // 这里我们尝试刷新文件缓冲区
      if (_logFile != null) {
        final sink = _logFile!.openWrite(mode: FileMode.append);
        await sink.flush();
        await sink.close();
      }
    } catch (e) {
      // Use logger instead of print for consistency
      logger.e("Error closing log file: $e");
    }
  }
}

class MyFilter extends LogFilter {
  @override
  bool shouldLog(LogEvent event) {
    return true;
  }
}

// 导出全局访问点
Logger get logger => LoggerService.logger;
