import 'dart:convert';
import 'dart:async';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/common.dart';

/// WebSocket连接管理器
/// 负责管理Flutter应用与浏览器扩展之间的WebSocket连接
class WebSocketManager extends GetxController {
  // 存储所有活跃的WebSocket连接
  final _connections = <dynamic>{}.obs;
  
  // 浏览器扩展连接（用于发送命令）
  final _browserExtensionConnections = <dynamic>{}.obs;
  
  // 连接状态
  final isConnected = false.obs;
  final connectionCount = 0.obs;
  
  @override
  void onInit() {
    super.onInit();
    final timestamp = DateTime.now().toIso8601String();
    logger.i('[$timestamp] WebSocket管理器初始化');
  }
  
  @override
  void onClose() {
    // 关闭所有连接
    closeAllConnections();
    super.onClose();
  }
  
  /// 添加WebSocket连接
  void addConnection(dynamic webSocket) {
    try {
      final timestamp = DateTime.now().toIso8601String();

      // 检查是否已存在相同连接，避免重复添加
      if (_connections.contains(webSocket)) {
        logger.w('[$timestamp] WebSocket连接已存在，跳过重复添加');
        return;
      }

      _connections.add(webSocket);
      connectionCount.value = _connections.length;
      isConnected.value = _connections.isNotEmpty;

      logger.i('[$timestamp] 添加WebSocket连接，当前连接数: ${connectionCount.value}');

      // 监听连接状态，自动识别浏览器扩展连接
      _identifyBrowserExtension(webSocket);
    } catch (e) {
      final timestamp = DateTime.now().toIso8601String();
      logger.e('[$timestamp] 添加WebSocket连接失败: $e');
    }
  }
  
  /// 移除WebSocket连接
  void removeConnection(dynamic webSocket) {
    try {
      final timestamp = DateTime.now().toIso8601String();

      final wasInConnections = _connections.remove(webSocket);
      final wasInBrowserExtensions = _browserExtensionConnections.remove(webSocket);

      connectionCount.value = _connections.length;
      isConnected.value = _connections.isNotEmpty;

      if (wasInConnections || wasInBrowserExtensions) {
        logger.i('[$timestamp] 移除WebSocket连接，当前连接数: ${connectionCount.value}, 浏览器扩展连接数: ${_browserExtensionConnections.length}');
      } else {
        logger.d('[$timestamp] 尝试移除不存在的WebSocket连接');
      }
    } catch (e) {
      final timestamp = DateTime.now().toIso8601String();
      logger.e('[$timestamp] 移除WebSocket连接失败: $e');
    }
  }
  
  /// 识别浏览器扩展连接
  void _identifyBrowserExtension(dynamic webSocket) {
    // 发送识别消息
    try {
      final timestamp = DateTime.now().toIso8601String();
      final identifyMessage = jsonEncode({
        'type': 'identify',
        'source': 'flutter_app',
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      });

      webSocket.sink.add(identifyMessage);

      logger.i('[$timestamp] 已发送浏览器扩展识别消息，等待响应');
      // 注意：不再立即添加到浏览器扩展连接列表
      // 等待 identify_response 消息确认后再添加
    } catch (e) {
      final timestamp = DateTime.now().toIso8601String();
      logger.e('[$timestamp] 发送浏览器扩展识别消息失败: $e');
    }
  }

  /// 确认浏览器扩展连接（收到identify_response后调用）
  void confirmBrowserExtensionConnection(dynamic webSocket, Map<String, dynamic> responseData) {
    try {
      final timestamp = DateTime.now().toIso8601String();

      // 验证响应数据
      if (responseData['source'] == 'browser_extension') {
        // 只有在收到正确的识别响应后才添加到浏览器扩展连接列表
        if (!_browserExtensionConnections.contains(webSocket)) {
          _browserExtensionConnections.add(webSocket);
          logger.i('[$timestamp] 浏览器扩展连接已确认并添加到管理器，扩展连接数: ${_browserExtensionConnections.length}');
        } else {
          logger.d('[$timestamp] 浏览器扩展连接已存在，跳过重复添加');
        }
      } else {
        logger.w('[$timestamp] 收到无效的浏览器扩展识别响应: ${responseData['source']}');
      }
    } catch (e) {
      final timestamp = DateTime.now().toIso8601String();
      logger.e('[$timestamp] 确认浏览器扩展连接失败: $e');
    }
  }
  
  /// 发送消息到所有浏览器扩展
  void sendToBrowserExtension(Map<String, dynamic> message) {
    final timestamp = DateTime.now().toIso8601String();

    if (_browserExtensionConnections.isEmpty) {
      logger.w('[$timestamp] 没有可用的浏览器扩展连接，总连接数: ${_connections.length}');
      return;
    }

    final messageJson = jsonEncode(message);
    int successCount = 0;
    int failureCount = 0;

    logger.d('[$timestamp] 准备发送消息到 ${_browserExtensionConnections.length} 个浏览器扩展连接');

    for (final connection in _browserExtensionConnections.toList()) {
      try {
        connection.sink.add(messageJson);
        successCount++;
        logger.d('[$timestamp] 成功发送消息到浏览器扩展连接 #$successCount');
      } catch (e) {
        failureCount++;
        logger.e('[$timestamp] 发送消息到浏览器扩展失败 #$failureCount: $e');
        // 移除失效的连接
        _browserExtensionConnections.remove(connection);
        _connections.remove(connection);
      }
    }

    logger.i('[$timestamp] 消息发送完成 - 成功: $successCount, 失败: $failureCount');

    // 更新连接计数
    connectionCount.value = _connections.length;
    isConnected.value = _connections.isNotEmpty;

    if (successCount == 0 && failureCount > 0) {
      logger.e('[$timestamp] 所有浏览器扩展连接都失败了');
      _showConnectionError();
    }
  }
  
  /// 发送视频控制命令到浏览器 (直接命令格式)
  void sendDirectVideoCommand(String commandType, {Map<String, dynamic>? params}) {
    final timestamp = DateTime.now().toIso8601String();

    logger.i('[$timestamp] 发送视频命令: $commandType, 连接状态: ${isConnected.value}, 浏览器扩展连接数: ${_browserExtensionConnections.length}');

    if (!isConnected.value) {
      logger.w('[$timestamp] WebSocket未连接，无法发送视频命令: $commandType');
      _showConnectionError();
      return;
    }

    if (_browserExtensionConnections.isEmpty) {
      logger.w('[$timestamp] 没有浏览器扩展连接，无法发送命令: $commandType');
      _showConnectionError();
      return;
    }

    final message = {
      'type': commandType,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      ...?params,
    };

    logger.i('[$timestamp] 发送视频命令到 ${_browserExtensionConnections.length} 个浏览器扩展: $commandType');
    logger.d('[$timestamp] 命令详情: $message');

    sendToBrowserExtension(message);
  }

  /// 显示连接错误提示
  void _showConnectionError() {
    try {
      final timestamp = DateTime.now().toIso8601String();
      logger.w('[$timestamp] 显示浏览器扩展连接错误提示');

      showLocalNotification(
        '浏览器扩展未连接',
        '请先在浏览器中连接Guru扩展，然后再使用键盘快捷键'
      );
    } catch (e) {
      final timestamp = DateTime.now().toIso8601String();
      logger.e('[$timestamp] 显示连接错误提示失败: $e');
    }
  }

  /// 显示需要先连接浏览器扩展的提示
  void showBrowserExtensionRequiredNotification() {
    try {
      final timestamp = DateTime.now().toIso8601String();
      logger.w('[$timestamp] 显示需要连接浏览器扩展的提示');

      showLocalNotification(
        '请先连接浏览器扩展',
        '在使用键盘快捷键控制浏览器视频前，请先在浏览器中打开Guru扩展并连接到应用'
      );
    } catch (e) {
      final timestamp = DateTime.now().toIso8601String();
      logger.e('[$timestamp] 显示浏览器扩展连接提示失败: $e');
    }
  }
  
  /// 请求浏览器截图
  void requestScreenshot() {
    if (!isConnected.value) {
      logger.w('WebSocket未连接，无法请求截图');
      _showConnectionError();
      return;
    }
    sendDirectVideoCommand('take_screenshot');
  }

  /// 请求浏览器时间戳
  void requestTimestamp() {
    if (!isConnected.value) {
      logger.w('WebSocket未连接，无法请求时间戳');
      _showConnectionError();
      return;
    }
    // 浏览器扩展期望的是 'generate_timestamp_link' 类型
    sendDirectVideoCommand('generate_timestamp_link');
  }

  /// 发送播放/暂停命令
  void sendPlayPause() {
    if (!isConnected.value) {
      logger.w('WebSocket未连接，无法发送播放/暂停命令');
      _showConnectionError();
      return;
    }
    sendDirectVideoCommand('toggle_play');
  }

  /// 发送跳转命令
  void sendSeek(int seconds, {bool relative = true}) {
    if (!isConnected.value) {
      logger.w('WebSocket未连接，无法发送跳转命令');
      _showConnectionError();
      return;
    }

    if (relative) {
      // 相对跳转：发送 seek_relative 命令
      sendDirectVideoCommand('seek_relative', params: {'seconds': seconds});
    } else {
      // 绝对跳转：发送 seek 命令 (浏览器扩展期望 time 参数)
      sendDirectVideoCommand('seek', params: {'time': seconds});
    }
  }

  /// 请求浏览器视频信息
  void requestVideoInfo() {
    if (!isConnected.value) {
      logger.w('WebSocket未连接，无法请求视频信息');
      _showConnectionError();
      return;
    }
    sendDirectVideoCommand('get_video_info');
  }

  /// 发送播放命令
  void sendPlay() {
    if (!isConnected.value) {
      logger.w('WebSocket未连接，无法发送播放命令');
      _showConnectionError();
      return;
    }
    sendDirectVideoCommand('play');
  }

  /// 发送暂停命令
  void sendPause() {
    if (!isConnected.value) {
      logger.w('WebSocket未连接，无法发送暂停命令');
      _showConnectionError();
      return;
    }
    sendDirectVideoCommand('pause');
  }

  /// 设置播放速度
  void setPlaybackRate(double rate) {
    if (!isConnected.value) {
      logger.w('WebSocket未连接，无法设置播放速度');
      _showConnectionError();
      return;
    }
    sendDirectVideoCommand('set_playback_rate', params: {'rate': rate});
  }

  /// 在浏览器中打开视频URL并跳转到指定时间戳
  void openVideoWithTimestamp(String videoUrl, int timestampSeconds) {
    if (!isConnected.value) {
      logger.w('WebSocket未连接，无法打开浏览器视频');
      _showConnectionError();
      return;
    }

    final timestamp = DateTime.now().toIso8601String();
    logger.i('[$timestamp] 请求浏览器打开视频: $videoUrl, 时间戳: $timestampSeconds秒');

    sendDirectVideoCommand('open_video_with_timestamp', params: {
      'url': videoUrl,
      'timestamp': timestampSeconds
    });
  }

  /// 在现有浏览器标签页中跳转到指定时间戳
  void seekToTimestampInBrowser(String videoUrl, int timestampSeconds) {
    if (!isConnected.value) {
      logger.w('WebSocket未连接，无法在浏览器中跳转');
      _showConnectionError();
      return;
    }

    final timestamp = DateTime.now().toIso8601String();
    logger.i('[$timestamp] 请求浏览器跳转到时间戳: $videoUrl, 时间戳: $timestampSeconds秒');

    sendDirectVideoCommand('seek_to_timestamp', params: {
      'url': videoUrl,
      'timestamp': timestampSeconds
    });
  }

  /// 智能导航到视频时间戳（自动选择最佳策略）
  /// 这个方法会让浏览器扩展决定是使用内页跳转还是页面刷新
  void navigateToVideoTimestamp(String videoUrl, int timestampSeconds) {
    if (!isConnected.value) {
      logger.w('WebSocket未连接，无法导航到视频');
      _showConnectionError();
      return;
    }

    final timestamp = DateTime.now().toIso8601String();
    logger.i('[$timestamp] 智能导航到视频时间戳: $videoUrl, 时间戳: $timestampSeconds秒');

    // 使用 openVideoWithTimestamp，但浏览器扩展现在会智能地处理同视频的情况
    sendDirectVideoCommand('open_video_with_timestamp', params: {
      'url': videoUrl,
      'timestamp': timestampSeconds
    });
  }
  
  /// 广播消息到所有连接
  void broadcast(Map<String, dynamic> message) {
    if (_connections.isEmpty) {
      logger.w('没有可用的WebSocket连接');
      return;
    }
    
    final messageJson = jsonEncode(message);
    int successCount = 0;
    
    for (final connection in _connections.toList()) {
      try {
        connection.sink.add(messageJson);
        successCount++;
      } catch (e) {
        logger.e('广播消息失败: $e');
        // 移除失效的连接
        removeConnection(connection);
      }
    }
    
    logger.i('消息已广播到 $successCount 个连接');
  }
  
  /// 关闭所有连接
  void closeAllConnections() {
    try {
      for (final connection in _connections.toList()) {
        try {
          connection.sink.close();
        } catch (e) {
          logger.e('关闭WebSocket连接失败: $e');
        }
      }
      
      _connections.clear();
      _browserExtensionConnections.clear();
      connectionCount.value = 0;
      isConnected.value = false;
      
      logger.i('所有WebSocket连接已关闭');
    } catch (e) {
      logger.e('关闭所有WebSocket连接失败: $e');
    }
  }
  
  /// 获取连接状态信息
  Map<String, dynamic> getConnectionInfo() {
    return {
      'total_connections': _connections.length,
      'browser_extension_connections': _browserExtensionConnections.length,
      'is_connected': isConnected.value,
      'browser_extension_available': _browserExtensionConnections.isNotEmpty,
    };
  }

  /// 检查浏览器扩展是否可用
  bool isBrowserExtensionAvailable() {
    final available = isConnected.value && _browserExtensionConnections.isNotEmpty;
    final timestamp = DateTime.now().toIso8601String();
    logger.d('[$timestamp] 浏览器扩展可用性检查: $available (WebSocket连接: ${isConnected.value}, 扩展连接数: ${_browserExtensionConnections.length})');
    return available;
  }


  /// 等待浏览器扩展连接（带超时）
  Future<bool> waitForBrowserExtension({Duration timeout = const Duration(seconds: 5)}) async {
    final timestamp = DateTime.now().toIso8601String();

    if (isBrowserExtensionAvailable()) {
      logger.d('[$timestamp] 浏览器扩展已可用，无需等待');
      return true;
    }

    logger.d('[$timestamp] 开始等待浏览器扩展连接，超时: ${timeout.inMilliseconds}ms');

    final completer = Completer<bool>();
    Timer? timer;
    StreamSubscription? connectionSubscription;
    StreamSubscription? extensionSubscription;

    void checkAndComplete() {
      if (isBrowserExtensionAvailable()) {
        timer?.cancel();
        connectionSubscription?.cancel();
        extensionSubscription?.cancel();
        if (!completer.isCompleted) {
          logger.d('[$timestamp] 浏览器扩展连接检测成功');
          completer.complete(true);
        }
      }
    }

    // 监听WebSocket连接状态变化
    connectionSubscription = isConnected.listen((connected) {
      logger.d('[$timestamp] WebSocket连接状态变化: $connected');
      checkAndComplete();
    });

    // 监听浏览器扩展连接变化
    extensionSubscription = _browserExtensionConnections.listen((connections) {
      logger.d('[$timestamp] 浏览器扩展连接数变化: ${connections.length}');
      checkAndComplete();
    });

    // 设置超时
    timer = Timer(timeout, () {
      connectionSubscription?.cancel();
      extensionSubscription?.cancel();
      if (!completer.isCompleted) {
        logger.d('[$timestamp] 等待浏览器扩展连接超时');
        completer.complete(false);
      }
    });

    return completer.future;
  }
}
