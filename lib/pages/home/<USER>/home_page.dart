import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'dart:math' as math;
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/controllers/common.dart';

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    // 在build方法内部定义所有工具项
    // 注意工具项
    final noteTools = [
      ToolItem(
        icon: LucideIcons.focus,
        title: 'home.tools.image_ocr'.tr,
        onTap: () => {Get.toNamed('/image_ocr')},
        isPro: true,
      ),
      ToolItem(
        icon: LucideIcons.video,
        title: 'home.tools.videoNote'.tr,
        onTap: () => {Get.toNamed('/video_note')},
        isPro: true,
      ),
      if (PathUtils.isDesktop)
        ToolItem(
          icon: FontAwesomeIcons.filePdf,
          title: 'home.tools.pdfNote'.tr,
          onTap: () => {Get.toNamed('/pdf_note')},
          isPro: true,
        ),
      ToolItem(
        icon: FontAwesomeIcons.lightbulb,
        title: 'home.tools.flashNote'.tr,
        onTap: () => {Get.toNamed('/flash_notes')},
        isPro: true,
      ),
    ];

    final ankiCardTools = [
      ToolItem(
        icon: LucideIcons.rocket,
        title: 'home.tools.aiCard'.tr,
        onTap: () => {Get.toNamed('/llm_card')},
        isPro: true,
      ),
      ToolItem(
        icon: FontAwesomeIcons.filePdf,
        title: 'home.tools.pdfCard'.tr,
        onTap: () => {Get.toNamed('/pdf_card')},
        isPro: true,
      ),
      ToolItem(
        icon: LucideIcons.fileType,
        title: 'home.tools.textCard'.tr,
        onTap: () => {Get.toNamed('/text_card')},
        isPro: true,
      ),
      ToolItem(
        icon: LucideIcons.fileText,
        title: 'home.tools.markdownCard'.tr,
        onTap: () => {
          Get.toNamed('/markdown_card'),
        },
        isPro: true,
      ),
      ToolItem(
        icon: LucideIcons.image,
        title: 'home.tools.imageCard'.tr,
        onTap: () => {Get.toNamed('/image_card')},
        isPro: true,
      ),
      ToolItem(
        icon: FontAwesomeIcons.fileWord,
        title: 'home.tools.wordCard'.tr,
        onTap: () => {
          Get.toNamed('/word_card'),
        },
        isPro: true,
      ),
      ToolItem(
        icon: FontAwesomeIcons.fileExcel,
        title: 'home.tools.excelCard'.tr,
        onTap: () => {
          Get.toNamed('/excel_card'),
        },
        isPro: true,
      ),
      ToolItem(
        icon: LucideIcons.share2,
        title: 'home.tools.mindmapCard'.tr,
        onTap: () => {
          Get.toNamed('/mindmap_card'),
        },
        isPro: true,
      ),
      // ToolItem(
      //   icon: LucideIcons.headphones,
      //   title: 'home.tool.vocab_card'.tr,
      //   onTap: () => {
      //     Get.toNamed('/vocab_card'),
      //   },
      //   isPro: true,
      // ),
      ToolItem(
        icon: LucideIcons.bookOpen,
        title: 'home.tools.wechatReaderCard'.tr,
        onTap: () => {
          Get.toNamed('/wereader_card'),
        },
        isPro: true,
      ),
      ToolItem(
        icon: LucideIcons.notebookPen,
        title: 'home.tools.mubuCard'.tr,
        onTap: () => {
          Get.toNamed('/mubu_card'),
        },
        isPro: true,
      ),
      ToolItem(
        icon: LucideIcons.clapperboard,
        title: 'home.tools.mediaCard'.tr,
        onTap: () => {
          Get.toNamed('/media_card'),
        },
        isPro: true,
      ),
    ];

    final ankiEnhanceTools = [
      if (PathUtils.isDesktop) ...[
        ToolItem(
          icon: LucideIcons.libraryBig,
          title: 'home.tools.deckManager'.tr,
          onTap: () => {Get.toNamed("/deck_manager")},
          isPro: true,
        ),
      ],
      ToolItem(
        icon: LucideIcons.volume2,
        title: 'home.tools.cardTts'.tr,
        onTap: () => {Get.toNamed("/card_tts")},
        isPro: true,
      ),
      if (PathUtils.isDesktop)
        ToolItem(
          icon: LucideIcons.focus,
          title: 'home.tools.cardOcr'.tr,
          onTap: () => {Get.toNamed("/card_ocr")},
          isPro: true,
        ),
      if (PathUtils.isDesktop)
        ToolItem(
          icon: LucideIcons.clapperboard,
          title: 'home.tools.card_media_manager'.tr,
          onTap: () => {Get.toNamed("/card_media_manager")},
          isPro: true,
        ),
      ToolItem(
        icon: LucideIcons.refreshCcw,
        title: 'home.tools.ankiSync'.tr,
        onTap: () => {Get.toNamed('/toolbox/sync')},
        isPro: true,
      ),
    ];

    final editTools = [
      ToolItem(
        icon: LucideIcons.merge,
        title: 'home.tools.pdfMerge'.tr,
        onTap: () => {Get.toNamed('/toolbox/merge')},
      ),
      ToolItem(
        icon: LucideIcons.scissors,
        title: 'home.tools.pdfSplit'.tr,
        onTap: () => {Get.toNamed('/toolbox/split')},
      ),
      ToolItem(
        icon: LucideIcons.share,
        title: 'home.tools.pdfExtract'.tr,
        onTap: () => {Get.toNamed('/toolbox/extract')},
      ),
      ToolItem(
        icon: LucideIcons.trash2,
        title: 'home.tools.pdfDelete'.tr,
        onTap: () => {Get.toNamed('/toolbox/delete')},
      ),
      ToolItem(
        icon: LucideIcons.arrowUp10,
        title: 'home.tools.pdfReorder'.tr,
        onTap: () => {Get.toNamed('/toolbox/reorder')},
      ),
      if (PathUtils.isDesktop)
        ToolItem(
          icon: LucideIcons.zoomIn,
          title: 'home.tools.pdfScale'.tr,
          onTap: () => {Get.toNamed('/toolbox/scale')},
        ),
      ToolItem(
        icon: LucideIcons.lock,
        title: 'home.tools.pdfEncrypt'.tr,
        onTap: () => {Get.toNamed('/toolbox/encrypt')},
      ),
      ToolItem(
        icon: LucideIcons.bookmark,
        title: 'home.tools.pdfBookmark'.tr,
        onTap: () => {Get.toNamed('/toolbox/bookmark')},
      ),
      if (PathUtils.isDesktop)
        ToolItem(
          icon: LucideIcons.crop,
          title: 'home.tools.pdfCrop'.tr,
          onTap: () => {Get.toNamed('/toolbox/crop')},
        ),
      if (PathUtils.isDesktop)
        ToolItem(
          icon: LucideIcons.expand,
          title: 'home.tools.pdfExpand'.tr,
          onTap: () => {Get.toNamed('/toolbox/expand')},
        ),
      if (PathUtils.isDesktop)
        ToolItem(
          icon: LucideIcons.lockOpen,
          title: 'home.tools.recoverPermission'.tr,
          onTap: () => {Get.toNamed('/toolbox/recover_permission')},
        ),
      ToolItem(
        icon: LucideIcons.rotateCw,
        title: 'home.tools.pdfRotate'.tr,
        onTap: () => {Get.toNamed('/toolbox/rotate')},
      ),
      ToolItem(
        icon: LucideIcons.penTool,
        title: 'home.tools.pdfAnnot'.tr,
        onTap: () => {Get.toNamed('/toolbox/annot')},
      ),
      ToolItem(
        icon: LucideIcons.badgePlus,
        title: 'home.tools.pdfWatermark'.tr,
        onTap: () => {Get.toNamed('/toolbox/watermark')},
      ),
      if (PathUtils.isDesktop)
        ToolItem(
          icon: LucideIcons.fileInput,
          title: 'home.tools.pdfInsert'.tr,
          onTap: () => {Get.toNamed('/toolbox/insert')},
        ),
      if (PathUtils.isDesktop)
        ToolItem(
          icon: LucideIcons.fileSpreadsheet,
          title: 'home.tools.pdfBackground'.tr,
          onTap: () => {Get.toNamed('/toolbox/background')},
        ),
      if (PathUtils.isDesktop)
        ToolItem(
          icon: LucideIcons.columns2,
          title: 'home.tools.pdfCut'.tr,
          onTap: () => {Get.toNamed('/toolbox/cut')},
        ),
      if (PathUtils.isDesktop)
        ToolItem(
          icon: LucideIcons.grid2x2,
          title: 'home.tools.pdfCombine'.tr,
          onTap: () => {Get.toNamed('/toolbox/combine')},
        ),
      ToolItem(
        icon: FontAwesomeIcons.listOl,
        title: 'home.tools.pdfPageNumber'.tr,
        onTap: () => {Get.toNamed('/toolbox/page_number')},
      ),
      if (PathUtils.isDesktop)
        ToolItem(
          icon: LucideIcons.info,
          title: 'home.tools.pdfMeta'.tr,
          onTap: () => {Get.toNamed('/toolbox/meta')},
        ),
      if (PathUtils.isDesktop)
        ToolItem(
          icon: LucideIcons.focus,
          title: 'home.tools.pdf_ocr'.tr,
          onTap: () => {Get.toNamed('/toolbox/ocr')},
        ),
    ];

    final convertTools = [
      ToolItem(
        icon: LucideIcons.image,
        title: 'home.tools.imgToPdf'.tr,
        onTap: () => {Get.toNamed('/toolbox/convert_img_to_pdf')},
      ),
      ToolItem(
        icon: LucideIcons.fileImage,
        title: 'home.tools.pdfToImg'.tr,
        onTap: () => {Get.toNamed('/toolbox/convert_pdf_to_img')},
      ),
      ToolItem(
        icon: LucideIcons.fileImage,
        title: 'home.tools.pdfToImgPdf'.tr,
        onTap: () => {Get.toNamed('/toolbox/convert_pdf_to_img_pdf')},
      ),
      if (PathUtils.isDesktop)
        ToolItem(
          icon: LucideIcons.bookOpenText,
          title: 'home.tools.epubToPdf'.tr,
          onTap: () => {Get.toNamed('/toolbox/convert_epub_to_pdf')},
        ),
      if (PathUtils.isDesktop)
        ToolItem(
          icon: LucideIcons.bookOpenText,
          title: 'home.tools.mobiToPdf'.tr,
          onTap: () => {Get.toNamed('/toolbox/convert_mobi_to_pdf')},
        ),
      // ToolItem(
      //   icon: FontAwesomeIcons.filePdf,
      //   title: 'OFD转PDF',
      //   onTap: () => {Get.toNamed('/toolbox/convert_ofd_to_pdf')},
      // ),
      // ToolItem(
      //   icon: LucideIcons.fileImage,
      //   title: 'DOCX转PDF',
      //   onTap: () => {Get.toNamed('/toolbox/convert_docx_to_pdf')},
      // ),
      if (PathUtils.isDesktop)
        ToolItem(
          icon: FontAwesomeIcons.filePdf,
          title: 'home.tools.pdfToDocx'.tr,
          onTap: () => {Get.toNamed('/toolbox/convert_pdf_to_docx')},
        ),
      ToolItem(
        icon: FontAwesomeIcons.fileWord,
        title: 'home.tools.docxToHtml'.tr,
        onTap: () => {Get.toNamed('/toolbox/convert_docx_to_html')},
      ),
      // ToolItem(
      //   icon: LucideIcons.globe,
      //   title: 'HTML转PDF',
      //   onTap: () => {Get.toNamed('/toolbox/convert_html_to_pdf')},
      // ),
      ToolItem(
        icon: LucideIcons.file,
        title: 'home.tools.mdToHtml'.tr,
        onTap: () => {Get.toNamed('/toolbox/convert_md_to_html')},
      ),
      // ToolItem(
      //   icon: LucideIcons.file,
      //   title: 'MD转PDF',
      //   onTap: () => {Get.toNamed('/toolbox/convert_md_to_pdf')},
      // ),
    ];

    final screenWidth = MediaQuery.of(context).size.width;

    // 1. 计算关键尺寸
    final allTools = [
      ...noteTools,
      ...ankiCardTools,
      ...ankiEnhanceTools,
      ...editTools,
      ...convertTools
    ];

    // 使用TextPainter计算实际文本宽度
    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
      textScaler: MediaQuery.of(context).textScaler,
    );

    double getTextWidth(String text, TextStyle style) {
      textPainter.text = TextSpan(text: text, style: style);
      textPainter.layout();
      return textPainter.width;
    }

    // 计算最长标题的实际宽度
    final titleStyle = Theme.of(context).textTheme.titleMedium;
    final maxTitleWidth = allTools.fold<double>(
      0,
      (max, tool) => math.max(
        max,
        getTextWidth(tool.title, titleStyle ?? const TextStyle(fontSize: 24)),
      ),
    );

    // 添加安全边距，确保文本完整显示
    const safetyPadding = 40.0;
    final adjustedMaxTitleWidth = maxTitleWidth + safetyPadding;

    // ListTile模式下单个卡片的最小宽度（图标 + 间距 + 最长文本宽度 + 箭头）
    final minListTileWidth = 24 + 16 + adjustedMaxTitleWidth + 16 + 24;
    // 理想卡片宽度与最小宽度相同，确保能完整显示最长标题
    final idealCardWidth = minListTileWidth;

    // 2. 确定显示模式和列数
    int columns;
    bool useIconMode;

    // 计算单列ListTile下可用于文本的宽度
    final singleColumnTextWidth = screenWidth - 86; // 总宽度减去其他元素占用

    if (singleColumnTextWidth >= maxTitleWidth) {
      // 可以使用ListTile模式
      useIconMode = false;

      // 计算理论上最大可能的列数，使用minListTileWidth作为最小宽度
      final maxPossibleColumns =
          ((screenWidth - 32 + 16) / (minListTileWidth + 16)).floor();
      columns = math.max(1, maxPossibleColumns); // 确保至少1列
    } else {
      // 使用图标模式
      useIconMode = true;
      const minIconWidth = 48.0; // 图标模式下单个卡片的最小宽度

      // 计算图标模式下可能的最大列数
      final maxIconColumns =
          ((screenWidth - 32 + 16) / (minIconWidth + 16)).floor();
      columns = math.max(1, maxIconColumns); // 确保至少1列
    }

    // 3. 计算最终布局参数
    final finalMaxWidth = columns == 1 && !useIconMode
        ? double.infinity // 单列ListTile模式时不限制宽度
        : math.min(screenWidth, (idealCardWidth + 16) * columns + 16);

    return Center(
      child: Container(
        constraints: columns == 1 && !useIconMode
            ? const BoxConstraints()
            : BoxConstraints(maxWidth: finalMaxWidth),
        child: ListView(
          children: [
            if (useIconMode && columns == 1) ...[
              // 单列图标模式
              SectionTitle(title: 'home.sections.notes'.tr),
              ...noteTools.map((tool) => Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    child: ToolCard(
                      tool: tool,
                      maxLines: 1,
                      isNarrow: true,
                    ),
                  )),
              const SizedBox(height: 16),
              SectionTitle(title: 'home.sections.ankiCards'.tr),
              ...ankiCardTools.map((tool) => Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    child: ToolCard(
                      tool: tool,
                      maxLines: 1,
                      isNarrow: true,
                    ),
                  )),
              const SizedBox(height: 16),
              SectionTitle(title: 'home.sections.ankiEnhance'.tr),
              ...ankiEnhanceTools.map((tool) => Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    child: ToolCard(
                      tool: tool,
                      maxLines: 1,
                      isNarrow: true,
                    ),
                  )),
              const SizedBox(height: 16),
              SectionTitle(title: 'home.sections.pdfEdit'.tr),
              ...editTools.map((tool) => Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    child: ToolCard(
                      tool: tool,
                      maxLines: 1,
                      isNarrow: true,
                    ),
                  )),
              const SizedBox(height: 16),
              SectionTitle(title: 'home.sections.conversion'.tr),
              ...convertTools.map((tool) => Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    child: ToolCard(
                      tool: tool,
                      maxLines: 1,
                      isNarrow: true,
                    ),
                  )),
              const SizedBox(height: 16),
            ] else if (columns == 1 && !useIconMode) ...[
              // 单列ListTile模式
              SectionTitle(title: 'home.sections.notes'.tr),
              ...noteTools.map((tool) => Padding(
                    padding:
                        const EdgeInsets.symmetric(vertical: 4, horizontal: 16),
                    child: ToolCard(
                      tool: tool,
                      maxLines: 1,
                      isNarrow: false,
                    ),
                  )),
              const SizedBox(height: 16),
              SectionTitle(title: 'home.sections.ankiCards'.tr),
              ...ankiCardTools.map((tool) => Padding(
                    padding:
                        const EdgeInsets.symmetric(vertical: 4, horizontal: 16),
                    child: ToolCard(
                      tool: tool,
                      maxLines: 1,
                      isNarrow: false,
                    ),
                  )),
              const SizedBox(height: 16),
              SectionTitle(title: 'home.sections.ankiEnhance'.tr),
              ...ankiEnhanceTools.map((tool) => Padding(
                    padding:
                        const EdgeInsets.symmetric(vertical: 4, horizontal: 16),
                    child: ToolCard(
                      tool: tool,
                      maxLines: 1,
                      isNarrow: false,
                    ),
                  )),
              const SizedBox(height: 16),
              SectionTitle(title: 'home.sections.pdfEdit'.tr),
              ...editTools.map((tool) => Padding(
                    padding:
                        const EdgeInsets.symmetric(vertical: 4, horizontal: 16),
                    child: ToolCard(
                      tool: tool,
                      maxLines: 1,
                      isNarrow: false,
                    ),
                  )),
              const SizedBox(height: 16),
              SectionTitle(title: 'home.sections.conversion'.tr),
              ...convertTools.map((tool) => Padding(
                    padding:
                        const EdgeInsets.symmetric(vertical: 4, horizontal: 16),
                    child: ToolCard(
                      tool: tool,
                      maxLines: 1,
                      isNarrow: false,
                    ),
                  )),
            ] else ...[
              // 网格布局（多列模式）
              SectionTitle(title: 'home.sections.notes'.tr),
              ToolGrid(
                tools: noteTools,
                columns: columns,
                maxLines: 1,
                maxWidth: finalMaxWidth,
                useIconMode: useIconMode,
              ),
              const SizedBox(height: 32),
              SectionTitle(title: 'home.sections.ankiCards'.tr),
              ToolGrid(
                tools: ankiCardTools,
                columns: columns,
                maxLines: 1,
                maxWidth: finalMaxWidth,
                useIconMode: useIconMode,
              ),
              const SizedBox(height: 32),
              SectionTitle(title: 'home.sections.ankiEnhance'.tr),
              ToolGrid(
                tools: ankiEnhanceTools,
                columns: columns,
                maxLines: 1,
                maxWidth: finalMaxWidth,
                useIconMode: useIconMode,
              ),
              const SizedBox(height: 32),
              SectionTitle(title: 'home.sections.pdfEdit'.tr),
              ToolGrid(
                tools: editTools,
                columns: columns,
                maxLines: 1,
                maxWidth: finalMaxWidth,
                useIconMode: useIconMode,
              ),
              const SizedBox(height: 32),
              SectionTitle(title: 'home.sections.conversion'.tr),
              ToolGrid(
                tools: convertTools,
                columns: columns,
                maxLines: 1,
                maxWidth: finalMaxWidth,
                useIconMode: useIconMode,
              ),
              const SizedBox(height: 16),
            ],
          ],
        ),
      ),
    );
  }
}

class SectionTitle extends StatelessWidget {
  final String title;

  const SectionTitle({
    super.key,
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
      child: Text(
        title,
        style: Theme.of(context)
            .textTheme
            .titleMedium
            ?.copyWith(fontWeight: FontWeight.bold),
      ),
    );
  }
}

class ToolGrid extends StatelessWidget {
  final List<ToolItem> tools;
  final int columns;
  final int maxLines;
  final double maxWidth;
  final bool useIconMode;

  const ToolGrid({
    super.key,
    required this.tools,
    required this.columns,
    required this.maxLines,
    required this.maxWidth,
    required this.useIconMode,
  });

  @override
  Widget build(BuildContext context) {
    final safeColumns = math.max(1, columns);

    // 修改卡片高度计算
    final cardWidth =
        math.max(48.0, (maxWidth - 32 - (safeColumns - 1) * 16) / safeColumns);
    final cardHeight = useIconMode ? 48.0 : 60.0; // 统一 ListTile 模式的高度为 56

    // 确保宽高比大于0
    final aspectRatio = math.max(0.1, cardWidth / cardHeight);

    return GridView.count(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: safeColumns,
      mainAxisSpacing: 16,
      crossAxisSpacing: 16,
      childAspectRatio: aspectRatio,
      children: tools
          .map((tool) => ToolCard(
                tool: tool,
                maxLines: maxLines,
                isNarrow: useIconMode,
              ))
          .toList(),
    );
  }
}

class ToolCard extends StatelessWidget {
  final ToolItem tool;
  final int maxLines;
  final bool isNarrow;

  const ToolCard({
    super.key,
    required this.tool,
    required this.maxLines,
    required this.isNarrow,
  });

  @override
  Widget build(BuildContext context) {
    final cardContent = ShadCard(
      padding: EdgeInsets.zero,
      columnMainAxisAlignment: MainAxisAlignment.center,
      columnCrossAxisAlignment: CrossAxisAlignment.center,
      rowMainAxisAlignment: MainAxisAlignment.center,
      rowCrossAxisAlignment: CrossAxisAlignment.center,
      child: Stack(
        children: [
          isNarrow
              ? InkWell(
                  onTap: tool.onTap,
                  child: Center(
                    child: Icon(
                      tool.icon,
                      size: 24,
                    ),
                  ),
                )
              : ListTile(
                  leading: Icon(
                    tool.icon,
                    size: 24,
                  ),
                  title: Text(
                    tool.title,
                    style: TextStyle(
                      fontSize: 16,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                    maxLines: maxLines,
                    overflow: TextOverflow.ellipsis,
                  ),
                  // trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: tool.onTap,
                ),
          if (tool.isPro)
            Positioned(
              top: 0,
              right: 2.5,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.secondary,
                  borderRadius: const BorderRadius.only(
                    topRight: Radius.circular(4),
                    bottomLeft: Radius.circular(4),
                  ),
                ),
                child: Text(
                  'PRO',
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onSecondary,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
        ],
      ),
    );

    return Tooltip(
      message: tool.title,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.9),
        borderRadius: BorderRadius.circular(4),
      ),
      textStyle: TextStyle(
        color: Theme.of(context).colorScheme.onSurfaceVariant,
        fontSize: 14,
      ),
      waitDuration: const Duration(milliseconds: 500),
      showDuration: const Duration(seconds: 2),
      verticalOffset: 20,
      child: cardContent,
    );
  }
}

class ToolItem {
  final IconData icon;
  final String title;
  final VoidCallback onTap;
  final bool isPro;

  ToolItem({
    required this.icon,
    required this.title,
    required this.onTap,
    this.isPro = false,
  });
}
