<!-- Author: <PERSON><PERSON><PERSON> -->
<!-- Github: https://github.com/kevin2li/<PERSON>-<PERSON><PERSON>-<PERSON>mplates -->
<link rel="stylesheet" href="__pico.blue.min.css">
<div id="mindmap">
    <section style="width: 100%;height: 100%">
        <article style="width: 100%;height: 100%;display: flex;flex-direction: column;">
            <header>
                <div class="row">
                    <div style="flex: 1" class="text-muted">
                        {{#Source}}
                        来源：{{Source}}
                        {{/Source}}
                    </div>
                    <a onclick="toggleConfig()">
                        <svg xmlns="http://www.w3.org/2000/svg" height="1em" fill="#6c757d" viewBox="0 0 512 512">
                            <path
                                d="M 256 0 Q 282 0 306 5 Q 312 6 321 10 Q 329 15 335 25 Q 338 30 340 37 L 349 75 L 349 75 Q 351 80 356 83 Q 361 86 366 85 L 404 74 L 404 74 Q 410 72 416 72 Q 428 72 436 77 Q 445 82 449 87 Q 482 125 498 174 Q 501 180 501 189 Q 502 199 496 209 Q 492 214 488 219 L 459 246 L 459 246 Q 456 250 456 256 Q 456 262 459 266 L 488 293 L 488 293 Q 492 298 496 303 Q 501 314 501 323 Q 501 333 498 338 Q 482 387 449 425 Q 445 430 436 435 Q 428 440 416 440 Q 410 440 404 438 L 366 427 L 366 427 Q 361 426 356 429 Q 351 432 349 437 L 340 475 L 340 475 Q 338 482 335 487 Q 329 497 321 502 Q 312 506 306 507 Q 282 512 256 512 Q 230 512 206 507 Q 200 506 191 502 Q 183 497 177 487 Q 174 482 172 475 L 163 437 L 163 437 Q 161 432 156 429 Q 151 426 146 427 L 108 438 L 108 438 Q 102 440 96 440 Q 84 440 76 435 Q 67 430 64 425 Q 30 387 14 338 Q 11 332 11 323 Q 10 313 16 303 Q 20 298 24 293 L 53 266 L 53 266 Q 56 262 56 256 Q 56 250 53 246 L 24 219 L 24 219 Q 20 214 16 209 Q 10 198 11 189 Q 11 179 14 174 Q 30 126 64 87 Q 67 82 76 77 Q 84 72 96 72 Q 102 72 108 74 L 146 85 L 146 85 Q 151 86 156 83 Q 161 80 163 75 L 172 37 L 172 37 Q 174 30 177 25 Q 183 15 191 10 Q 200 6 206 5 Q 230 0 256 0 L 256 0 Z M 218 51 L 210 87 L 218 51 L 210 87 Q 203 111 180 124 Q 157 137 132 131 L 98 121 L 98 121 Q 73 150 60 187 L 86 212 L 86 212 Q 104 230 104 256 Q 104 282 86 301 L 60 325 L 60 325 Q 73 362 98 391 L 133 381 L 133 381 Q 157 374 180 388 Q 203 401 210 425 L 218 461 L 218 461 Q 256 467 294 461 L 303 425 L 303 425 Q 310 401 332 388 Q 355 375 380 381 L 414 391 L 414 391 Q 439 362 452 325 L 426 300 L 426 300 Q 408 282 408 256 Q 408 230 426 211 L 452 187 L 452 187 Q 439 150 414 121 L 380 131 L 380 131 Q 355 138 332 124 Q 310 111 303 86 L 294 51 L 294 51 Q 256 45 218 51 L 218 51 Z M 208 256 Q 209 283 232 298 Q 256 310 280 298 Q 303 283 304 256 Q 303 229 280 214 Q 256 202 232 214 Q 209 229 208 256 L 208 256 Z M 256 352 Q 230 352 208 339 L 208 339 L 208 339 Q 186 326 173 304 Q 160 281 160 256 Q 160 231 173 208 Q 186 186 208 173 Q 230 160 256 160 Q 282 160 304 173 Q 326 186 339 208 Q 352 231 352 256 Q 352 281 339 304 Q 326 326 304 339 Q 282 352 256 352 L 256 352 Z" />
                        </svg>
                    </a>
                </div>
            </header>
            <article style="width: 100%;flex: 1;">
                <svg id="markmap" style="width: 100%;height: 100%"></svg>
            </article>
            <div class="lower">
                <a onclick="expand_toggle()">
                    <!-- 展开/折叠全部节点 -->
                    <svg t="1723425251651" class="icon" viewBox="0 0 1024 1024" version="1.1"
                        xmlns="http://www.w3.org/2000/svg" p-id="22914" width="2em" height="2em">
                        <path
                            d="M785.066667 716.8c-64.853333 0-116.053333 44.373333-133.12 102.4H409.6c-58.026667 0-102.4-44.373333-102.4-102.4v-170.666667h344.746667c13.653333 58.026667 68.266667 102.4 133.12 102.4 75.093333 0 136.533333-61.44 136.533333-136.533333s-61.44-136.533333-136.533333-136.533333c-64.853333 0-116.053333 44.373333-133.12 102.4H307.2v-102.4-3.413334c78.506667-17.066667 136.533333-85.333333 136.533333-167.253333 0-95.573333-75.093333-170.666667-170.666666-170.666667S102.4 109.226667 102.4 204.8c0 81.92 58.026667 150.186667 136.533333 167.253333V716.8c0 95.573333 75.093333 170.666667 170.666667 170.666667h242.346667c13.653333 58.026667 68.266667 102.4 133.12 102.4 75.093333 0 136.533333-61.44 136.533333-136.533334s-61.44-136.533333-136.533333-136.533333z m0-273.066667c37.546667 0 68.266667 30.72 68.266666 68.266667s-30.72 68.266667-68.266666 68.266667-68.266667-30.72-68.266667-68.266667 30.72-68.266667 68.266667-68.266667zM170.666667 204.8c0-58.026667 44.373333-102.4 102.4-102.4s102.4 44.373333 102.4 102.4-44.373333 102.4-102.4 102.4-102.4-44.373333-102.4-102.4z m614.4 716.8c-37.546667 0-68.266667-30.72-68.266667-68.266667s30.72-68.266667 68.266667-68.266666 68.266667 30.72 68.266666 68.266666-30.72 68.266667-68.266666 68.266667z"
                            fill="#f0f0f0" p-id="22915"></path>
                    </svg>
                </a>
                <a onclick="goto_parent()">
                    <!-- 回到父节点 -->
                    <svg t="1723534925324" class="icon" viewBox="0 0 1024 1024" version="1.1"
                        xmlns="http://www.w3.org/2000/svg" p-id="9372" width="2em" height="2em">
                        <path
                            d="M958.464 884.736H558.08a142.336 142.336 0 0 1-141.312-142.336V204.8l237.568 232.448a48.128 48.128 0 0 0 35.84 14.336 47.104 47.104 0 0 0 35.84-14.336 51.2 51.2 0 0 0 0-72.704l-307.2-307.2a51.2 51.2 0 0 0-71.68 0l-307.2 312.32a51.2 51.2 0 0 0 0 72.704 51.2 51.2 0 0 0 71.68 0L314.368 225.28v517.12a244.736 244.736 0 0 0 243.712 244.736h400.384a51.2 51.2 0 0 0 51.2-51.2 51.2 51.2 0 0 0-51.2-51.2z"
                            fill="#f0f0f0" p-id="9373"></path>
                    </svg>
                </a>
                <a onclick="goto_current()">
                    <!-- 回到初始节点 -->
                    <svg t="1723425106111" class="icon" viewBox="0 0 1024 1024" version="1.1"
                        xmlns="http://www.w3.org/2000/svg" p-id="13846" width="2em" height="2em">
                        <path
                            d="M829.952 475.733333a320.128 320.128 0 0 0-281.685333-281.685333V106.666667a36.266667 36.266667 0 0 0-72.533334 0v87.381333a320.128 320.128 0 0 0-281.685333 281.685333H128a36.266667 36.266667 0 0 0 0 72.533334h66.048a320.128 320.128 0 0 0 281.685333 281.685333V896a36.266667 36.266667 0 1 0 72.533334 0v-66.048a320.128 320.128 0 0 0 281.685333-281.685333H896a36.266667 36.266667 0 1 0 0-72.533334h-66.048z m-65.962667 36.266667a251.989333 251.989333 0 1 1-503.978666 0 251.989333 251.989333 0 0 1 503.978666 0zM512 618.666667a106.666667 106.666667 0 1 0 0-213.333334 106.666667 106.666667 0 0 0 0 213.333334z"
                            fill="#f0f0f0" p-id="13847"></path>
                    </svg>
                </a>
                <a onclick="fold_toggle()">
                    <!-- 展开/折叠 -->
                    <svg t="1723424863782" class="icon" viewBox="0 0 1024 1024" version="1.1"
                        xmlns="http://www.w3.org/2000/svg" p-id="6170" width="2em" height="2em">
                        <path
                            d="M222.991059 707.463529a43.128471 43.128471 0 0 1 57.404235 0l231.785412 219.256471 231.845647-218.774588a43.128471 43.128471 0 0 1 57.344 0 35.719529 35.719529 0 0 1 0 53.007059L540.611765 1007.134118a43.128471 43.128471 0 0 1-57.344 0l-260.818824-246.061177A38.189176 38.189176 0 0 1 210.823529 734.268235c0-9.878588 4.035765-19.275294 12.16753-26.804706z m672.346353-192.692705a37.948235 37.948235 0 0 1 38.309647 37.225411 37.948235 37.948235 0 0 1-38.309647 37.285647H128.662588A37.948235 37.948235 0 0 1 90.352941 551.996235a37.948235 37.948235 0 0 1 38.309647-37.225411h766.674824zM540.611765 65.114353l260.818823 246.061176a35.719529 35.719529 0 0 1 0 53.007059 43.128471 43.128471 0 0 1-57.404235 0L512.180706 145.468235l-231.785412 219.256471a43.128471 43.128471 0 0 1-57.404235 0A36.020706 36.020706 0 0 1 210.823529 337.92c0-9.396706 4.035765-19.275294 11.685647-26.744471l260.758589-246.061176a43.128471 43.128471 0 0 1 57.344 0z"
                            p-id="6171" fill="#f0f0f0"></path>
                    </svg>
                </a>
                <a id="preview_btn" onclick="preview_toggle()">
                    <!-- 显示/隐藏挖空 -->
                    <svg t="1723513150667" class="icon" viewBox="0 0 1024 1024" version="1.1"
                        xmlns="http://www.w3.org/2000/svg" p-id="2627" id="mx_n_1723513150668" width="2em" height="2em">
                        <path
                            d="M512 844.606c-58.519 0-118.836-16.271-179.277-48.363-46.915-24.909-94.073-59.416-140.164-102.56C114.987 621.071 67.13 549.159 65.13 546.132a37.002 37.002 0 0 1 0-40.795c2-3.026 49.857-74.938 127.429-147.551 46.091-43.144 93.249-77.65 140.164-102.56 60.441-32.092 120.759-48.363 179.277-48.363s118.836 16.271 179.277 48.363c46.915 24.909 94.073 59.416 140.164 102.56C909.012 430.398 956.87 502.31 958.87 505.337a37.002 37.002 0 0 1 0 40.795c-2 3.026-49.857 74.938-127.429 147.551-46.091 43.144-93.249 77.65-140.164 102.56-60.441 32.091-120.758 48.363-179.277 48.363z m-370.487-318.87c46.516 62.165 197.934 244.87 370.487 244.87 172.541 0 323.95-182.679 370.486-244.871C835.95 463.543 684.541 280.864 512 280.864S188.049 463.543 141.513 525.736z"
                            fill="#f0f0f0" p-id="2628"></path>
                        <path
                            d="M512 687.889c-89.412 0-162.154-72.742-162.154-162.154S422.588 363.581 512 363.581s162.154 72.742 162.154 162.154S601.412 687.889 512 687.889z m0-250.308c-48.608 0-88.154 39.546-88.154 88.154s39.546 88.154 88.154 88.154 88.154-39.546 88.154-88.154-39.546-88.154-88.154-88.154z"
                            fill="#f0f0f0" p-id="2629"></path>
                    </svg>
                </a>
            </div>
        </article>

    </section>
</div>
<div id="outline">
    <section>
        <article>
            <header style="padding: 0 0.5em;">
                <div class="row">
                    <div style="flex: 1" class="text-muted">
                        {{#Source}}
                        来源：{{Source}}
                        {{/Source}}
                    </div>
                    <a onclick="toggleConfig()" style="cursor: pointer">
                        <svg xmlns="http://www.w3.org/2000/svg" height="1em" fill="#6c757d" viewBox="0 0 512 512">
                            <path
                                d="M 256 0 Q 282 0 306 5 Q 312 6 321 10 Q 329 15 335 25 Q 338 30 340 37 L 349 75 L 349 75 Q 351 80 356 83 Q 361 86 366 85 L 404 74 L 404 74 Q 410 72 416 72 Q 428 72 436 77 Q 445 82 449 87 Q 482 125 498 174 Q 501 180 501 189 Q 502 199 496 209 Q 492 214 488 219 L 459 246 L 459 246 Q 456 250 456 256 Q 456 262 459 266 L 488 293 L 488 293 Q 492 298 496 303 Q 501 314 501 323 Q 501 333 498 338 Q 482 387 449 425 Q 445 430 436 435 Q 428 440 416 440 Q 410 440 404 438 L 366 427 L 366 427 Q 361 426 356 429 Q 351 432 349 437 L 340 475 L 340 475 Q 338 482 335 487 Q 329 497 321 502 Q 312 506 306 507 Q 282 512 256 512 Q 230 512 206 507 Q 200 506 191 502 Q 183 497 177 487 Q 174 482 172 475 L 163 437 L 163 437 Q 161 432 156 429 Q 151 426 146 427 L 108 438 L 108 438 Q 102 440 96 440 Q 84 440 76 435 Q 67 430 64 425 Q 30 387 14 338 Q 11 332 11 323 Q 10 313 16 303 Q 20 298 24 293 L 53 266 L 53 266 Q 56 262 56 256 Q 56 250 53 246 L 24 219 L 24 219 Q 20 214 16 209 Q 10 198 11 189 Q 11 179 14 174 Q 30 126 64 87 Q 67 82 76 77 Q 84 72 96 72 Q 102 72 108 74 L 146 85 L 146 85 Q 151 86 156 83 Q 161 80 163 75 L 172 37 L 172 37 Q 174 30 177 25 Q 183 15 191 10 Q 200 6 206 5 Q 230 0 256 0 L 256 0 Z M 218 51 L 210 87 L 218 51 L 210 87 Q 203 111 180 124 Q 157 137 132 131 L 98 121 L 98 121 Q 73 150 60 187 L 86 212 L 86 212 Q 104 230 104 256 Q 104 282 86 301 L 60 325 L 60 325 Q 73 362 98 391 L 133 381 L 133 381 Q 157 374 180 388 Q 203 401 210 425 L 218 461 L 218 461 Q 256 467 294 461 L 303 425 L 303 425 Q 310 401 332 388 Q 355 375 380 381 L 414 391 L 414 391 Q 439 362 452 325 L 426 300 L 426 300 Q 408 282 408 256 Q 408 230 426 211 L 452 187 L 452 187 Q 439 150 414 121 L 380 131 L 380 131 Q 355 138 332 124 Q 310 111 303 86 L 294 51 L 294 51 Q 256 45 218 51 L 218 51 Z M 208 256 Q 209 283 232 298 Q 256 310 280 298 Q 303 283 304 256 Q 303 229 280 214 Q 256 202 232 214 Q 209 229 208 256 L 208 256 Z M 256 352 Q 230 352 208 339 L 208 339 L 208 339 Q 186 326 173 304 Q 160 281 160 256 Q 160 231 173 208 Q 186 186 208 173 Q 230 160 256 160 Q 282 160 304 173 Q 326 186 339 208 Q 352 231 352 256 Q 352 281 339 304 Q 326 326 304 339 Q 282 352 256 352 L 256 352 Z" />
                        </svg>
                    </a>
                </div>
            </header>
            <article>
                {{Front}}
                <footer>
                    <div id="deck_container" style="display: none;">
                    </div>
                    <div id="tag_container" style="display: none;">
                    </div>
                    <div id="time_container" style="display: none;">00:00
                    </div>
                </footer>
            </article>
        </article>
    </section>
</div>

<dialog id="config-dialog" class="modal" style="backdrop-filter: blur(0.05rem);">
    <article>
        <header>
            <button aria-label="Close" rel="prev" onclick="toggleConfig()"></button>
            <p>
                <svg xmlns="http://www.w3.org/2000/svg" height="1em" fill="currentColor" viewBox="0 0 512 512">
                    <path
                        d="M 256 0 Q 282 0 306 5 Q 312 6 321 10 Q 329 15 335 25 Q 338 30 340 37 L 349 75 L 349 75 Q 351 80 356 83 Q 361 86 366 85 L 404 74 L 404 74 Q 410 72 416 72 Q 428 72 436 77 Q 445 82 449 87 Q 482 125 498 174 Q 501 180 501 189 Q 502 199 496 209 Q 492 214 488 219 L 459 246 L 459 246 Q 456 250 456 256 Q 456 262 459 266 L 488 293 L 488 293 Q 492 298 496 303 Q 501 314 501 323 Q 501 333 498 338 Q 482 387 449 425 Q 445 430 436 435 Q 428 440 416 440 Q 410 440 404 438 L 366 427 L 366 427 Q 361 426 356 429 Q 351 432 349 437 L 340 475 L 340 475 Q 338 482 335 487 Q 329 497 321 502 Q 312 506 306 507 Q 282 512 256 512 Q 230 512 206 507 Q 200 506 191 502 Q 183 497 177 487 Q 174 482 172 475 L 163 437 L 163 437 Q 161 432 156 429 Q 151 426 146 427 L 108 438 L 108 438 Q 102 440 96 440 Q 84 440 76 435 Q 67 430 64 425 Q 30 387 14 338 Q 11 332 11 323 Q 10 313 16 303 Q 20 298 24 293 L 53 266 L 53 266 Q 56 262 56 256 Q 56 250 53 246 L 24 219 L 24 219 Q 20 214 16 209 Q 10 198 11 189 Q 11 179 14 174 Q 30 126 64 87 Q 67 82 76 77 Q 84 72 96 72 Q 102 72 108 74 L 146 85 L 146 85 Q 151 86 156 83 Q 161 80 163 75 L 172 37 L 172 37 Q 174 30 177 25 Q 183 15 191 10 Q 200 6 206 5 Q 230 0 256 0 L 256 0 Z M 218 51 L 210 87 L 218 51 L 210 87 Q 203 111 180 124 Q 157 137 132 131 L 98 121 L 98 121 Q 73 150 60 187 L 86 212 L 86 212 Q 104 230 104 256 Q 104 282 86 301 L 60 325 L 60 325 Q 73 362 98 391 L 133 381 L 133 381 Q 157 374 180 388 Q 203 401 210 425 L 218 461 L 218 461 Q 256 467 294 461 L 303 425 L 303 425 Q 310 401 332 388 Q 355 375 380 381 L 414 391 L 414 391 Q 439 362 452 325 L 426 300 L 426 300 Q 408 282 408 256 Q 408 230 426 211 L 452 187 L 452 187 Q 439 150 414 121 L 380 131 L 380 131 Q 355 138 332 124 Q 310 111 303 86 L 294 51 L 294 51 Q 256 45 218 51 L 218 51 Z M 208 256 Q 209 283 232 298 Q 256 310 280 298 Q 303 283 304 256 Q 303 229 280 214 Q 256 202 232 214 Q 209 229 208 256 L 208 256 Z M 256 352 Q 230 352 208 339 L 208 339 L 208 339 Q 186 326 173 304 Q 160 281 160 256 Q 160 231 173 208 Q 186 186 208 173 Q 230 160 256 160 Q 282 160 304 173 Q 326 186 339 208 Q 352 231 352 256 Q 352 281 339 304 Q 326 326 304 339 Q 282 352 256 352 L 256 352 Z" />
                </svg>
                <strong> 配置</strong>
            </p>
        </header>
        <section>
            <section class="row">
                <label for="mindmap_mode">导图模式</label>
                <input id="mindmap_mode" type="checkbox" role="switch" data-config-key="mindmap_mode" />
            </section>
            <section class="row">
                <label for="dark_mode">暗黑模式</label>
                <input id="dark_mode" type="checkbox" role="switch" data-config-key="dark_mode" />
            </section>
            <section class="row">
                <label for="show_deck">显示牌组</label>
                <input id="show_deck" type="checkbox" role="switch" data-config-key="show_deck" />
            </section>
            <section class="row">
                <label for="show_tag">显示标签</label>
                <input id="show_tag" type="checkbox" role="switch" data-config-key="show_tag" />
            </section>
            <section class="row">
                <label for="show_time">显示用时</label>
                <input id="show_time" type="checkbox" role="switch" data-config-key="show_time" />
            </section>
        </section>
    </article>
</dialog>
<script src="__{{MapID}}.js" type="text/javascript"></script>
<script>
    initializeConfig();
    var path = JSON.parse(`{{Path}}`);
    var fold_flag = false;
    var show_flag = false;
    var expand_flag = true;
    var current_path = path;
    var mode = localStorage.getItem("mindmap_mode") ?? "false";
    function render(path, level) {
        let data = window.sessionStorage.getItem('mindmap_data');
        let mind_data = JSON.parse(data);
        var node = mind_data;
        if (path != null && path != undefined && path.length > 0) {
            for (const p of path) {
                node = node.children[p];
            }
        }

        console.log({
            mind_data
        });
        const svgEl = document.querySelector('#markmap');
        svgEl.innerHTML = '';

        window.markmap.Markmap.create(svgEl, {
            initialExpandLevel: level, maxWidth: 300, duration: 0
        }

            , node);
    }

    function goto_parent() {
        if (current_path.length > 0) {
            current_path.pop();
            fold_flag = false;
            render(current_path, 1);
        }

        else {
            alert("已经是根节点了");
        }
    }

    function goto_current() {
        let path = JSON.parse(`{{Path}}`);
        current_path = path;
        fold_flag = false;
        expand_flag = true;
        render(path, 1)
    }

    function expand_toggle() {
        if (expand_flag) {
            render([], undefined);
            fold_flag = true;
        }

        else {
            current_path = [];
            render([], 1);
            fold_flag = false;
        }

        expand_flag = !expand_flag;
    }

    function fold_toggle() {
        if (fold_flag) {
            render(current_path, 1)
        }

        else {
            render(current_path, 2)
        }

        fold_flag = !fold_flag;
    }

    function preview_toggle() {
        let btn = document.querySelector('#preview_btn');
        console.log(show_flag);
        const eyeOpen = document.getElementById('eyeOpen'),
            eyeClosed = document.getElementById('eyeClosed');
        eyeOpen.style.display = show_flag ? 'flex' : 'none';
        eyeClosed.style.display = show_flag ? 'none' : 'flex';

        if (show_flag) {
            document.querySelectorAll("span.cloze").forEach(ele => {
                if (ele.classList.contains("activated")) {
                    ele.classList.toggle("activated");
                }
            });
        }

        else {
            document.querySelectorAll("span.cloze").forEach(ele => {
                if (!ele.classList.contains("activated")) {
                    ele.classList.toggle("activated");
                }
            });
        }

        show_flag = !show_flag;
    }

    function init() {
        let mindmap_mode = localStorage.getItem("mindmap_mode") ?? "false";
        if (mindmap_mode === "true") {
            document.getElementById("outline").style.display = "none";
            document.getElementById("mindmap").style.display = "flex";
            goto_current();
        }
        else {
            document.getElementById("mindmap").style.display = "none";
            document.getElementById("outline").style.display = "flex";
        }
        // 主题
        let is_dark_mode = localStorage.getItem("dark_mode") ?? "false";
        let ele = document.querySelector("html");
        if (is_dark_mode === "true") {
            ele.setAttribute("data-theme", "dark");
        } else {
            ele.setAttribute("data-theme", "light");
        }
        // 显示牌组
        let deck_container = document.getElementById("deck_container");
        let deck = "{{Deck}}";
        deck_container.innerText = `${deck.replaceAll("::", " / ")}`;
        // 显示标签
        let tag_container = document.getElementById("tag_container");
        let tags = `{{Tags}}`;
        if (tags.trim() !== "") {
            const tagArray = tags.split(' ').map(tag => tag.trim()).filter(tag => tag !== ""); // 过滤掉空字符串
            const tagSpans = tagArray.map(tag => `<span class="tag">${tag}</span>`).join('');
            tag_container.innerHTML = `${tagSpans}`;
        } else {
            tag_container.innerHTML = "暂无标签";
        }
        // 显示用时
        let time_container = document.getElementById("time_container");
        // 创建定时器，每秒更新一次，格式化为00:00格式，从0秒开始
        localStorage.setItem("time_elapsed", 0);
        let timer = setInterval(function () {
            let total_seconds = Number.parseInt(localStorage.getItem("time_elapsed") || "0");
            total_seconds++;
            let minutes = Math.floor(total_seconds / 60);
            let seconds = total_seconds % 60;
            time_container.innerText = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            localStorage.setItem("time_elapsed", total_seconds);
        }, 1000);
        localStorage.setItem("timer_interval", timer);
    }
    init();
</script>
