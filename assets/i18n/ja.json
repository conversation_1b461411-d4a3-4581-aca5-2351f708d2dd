{"about.bilibili": "Bilibili", "about.checkUpdate": "アップデートを確認", "about.communication": "交流チャンネル", "about.copyright": "Copyright © 2023-2025 Kevin2li", "about.friendlyLinks": "関連リンク", "about.github": "<PERSON><PERSON><PERSON>", "about.officialWebsite": "公式サイト", "about.paidUsersOnly": "有料ユーザー限定。参加の際は「購入プラットフォーム＋注文番号」を記載してください。例：淘宝xx。", "about.privacyPolicy": "プライバシーポリシー", "about.qqChannel": "QQチャンネル", "about.qqGroup": "QQグループ", "about.scanQrCode": "下のQRコードをスキャンして参加", "about.termsOfService": "利用規約", "about.title": "アプリについて＆ヘルプ", "about.version": "バージョン", "about.wechatGroup": "WeChatグループ", "activate.activate": "アクティベート", "activate.activate_time": "アクティベート時間", "activate.activated": "アクティベート済み", "activate.activationCode": "アクティベーションコード", "activate.activationInfo": "アクティベーション情報", "activate.allowed_divices": "許可デバイス数", "activate.buyActivationCode": "アクティベーションコードを購入しますか？", "activate.deactivate.cancel": "キャンセル", "activate.deactivate.confirm": "登録解除を確認", "activate.deactivate.description": "ご注意：\n1. 登録を解除すると、現在のデバイスのアクティベーション枠が解放されます。\n2. 登録解除後、OSを再インストールしない限り、同じアクティベーションコードでこのデバイスを再度アクティベートすることはできません。\nこのデバイスの登録を解除してもよろしいですか？", "activate.deactivate.title": "デバイスの登録解除", "activate.enterActivationCode": "アクティベーションコードを入力してください", "activate.faq": "よくある質問", "activate.not_activated": "未アクティベート", "activate.purchase.goToWebsite": "公式サイトへ", "activate.purchase.officialWebsite": "公式サイトでの購入:", "activate.purchase.onlineStore": "オンラインストアでの購入:", "activate.purchase.pdd": "拼多多(Pinduoduo)", "activate.purchase.taobao": "淘宝(Taobao)", "activate.purchase.title": "アクティベーションコードの購入", "activate.requestTrial": "トライアルを申請", "activate.state": "アクティベート状態", "activate.title": "アクティベーション", "activate.trialRequest.cancel": "キャンセル", "activate.trialRequest.confirm": "確認", "activate.trialRequest.description": "トライアル期間は3日間で、すべての機能を体験できます。トライアル終了後は、アクティベーションコードを購入して引き続き使用する必要があります。", "activate.trialRequest.title": "トライアル申請", "activate.unregister_device": "このデバイスの登録を解除", "anki.card_media_manager.at_least_one_card_type": "少なくとも1つのメディアタイプを選択してください", "anki.card_media_manager.compressing_images": "画像を圧縮中", "anki.card_media_manager.compression_completed": "圧縮完了：@success/@total ファイル成功", "anki.card_media_manager.compression_failed": "画像圧縮に失敗しました：@error", "anki.card_media_manager.compression_source": "ソース", "anki.card_media_manager.compression_tab": "画像圧縮", "anki.card_media_manager.copying_file": "ファイルをコピー中：@current/@total - @filename", "anki.card_media_manager.deck": "デッ<PERSON>", "anki.card_media_manager.deck_compression_completed": "デッキ圧縮完了：@success/@total ファイル成功", "anki.card_media_manager.deck_compression_completed_with_updates": "デッキ圧縮完了：@compressed 個の画像を圧縮、@updated/@total 枚のカードを更新", "anki.card_media_manager.deck_compression_failed": "デッキ圧縮に失敗しました：@error", "anki.card_media_manager.description": "デッキ内の画像圧縮、デッキ内画像の画像ホスティングへの自動アップロード、デッキ内画像の抽出などをサポート", "anki.card_media_manager.failed_to_get_media_path": "Ankiメディアパスの取得に失敗しました", "anki.card_media_manager.file": "ファイル", "anki.card_media_manager.function_description": "機能説明", "anki.card_media_manager.getting_deck_cards": "デッキカードを取得中", "anki.card_media_manager.include_audio": "音声を含む", "anki.card_media_manager.include_network_image": "ネットワーク画像を含む", "anki.card_media_manager.include_video": "動画を含む", "anki.card_media_manager.initializing": "初期化中", "anki.card_media_manager.invalid_quality": "圧縮品質が無効です。1-100の値を入力してください", "anki.card_media_manager.invalid_source_mode": "無効なソースモード", "anki.card_media_manager.media_type_local_audio": "ローカル音声", "anki.card_media_manager.media_type_local_image": "ローカル画像", "anki.card_media_manager.media_type_local_video": "ローカル動画", "anki.card_media_manager.media_type_network_audio": "ネットワーク音声", "anki.card_media_manager.media_type_network_image": "ネットワーク画像", "anki.card_media_manager.media_type_network_video": "ネットワーク動画", "anki.card_media_manager.media_types": "メディアタイプ", "anki.card_media_manager.no_cards_found": "カードが見つかりません", "anki.card_media_manager.no_deck_selected": "デッキが選択されていません", "anki.card_media_manager.no_files_selected": "ファイルが選択されていません", "anki.card_media_manager.no_media_found": "メディアファイルが見つかりません", "anki.card_media_manager.no_output_dir": "出力ディレクトリが設定されていません", "anki.card_media_manager.no_valid_files": "有効なファイルがありません", "anki.card_media_manager.picgo_address": "PicGoアドレス", "anki.card_media_manager.picgo_address_cannot_empty": "アドレスは空にできません", "anki.card_media_manager.picgo_address_placeholder": "PicGo接続アドレスを入力", "anki.card_media_manager.quality": "圧縮品質", "anki.card_media_manager.quality_error": "正の整数を入力してください", "anki.card_media_manager.quality_placeholder": "圧縮品質を設定、0~100の間", "anki.card_media_manager.scan_completed": "スキャン完了：@success/@total ファイル成功、@failed 失敗", "anki.card_media_manager.scan_failed": "画像スキャンに失敗しました", "anki.card_media_manager.scan_tab": "メディアスキャン", "anki.card_media_manager.scanning_cards": "カードをスキャン中：@current/@total", "anki.card_media_manager.scanning_deck": "デッキをスキャン中", "anki.card_media_manager.title": "メディア管理", "anki.card_media_manager.updating_note_references": "ノート参照を更新中：@current/@total", "anki.card_media_manager.upload_completed": "アップロード完了：@success/@total ファイル成功、@failed 失敗", "anki.card_media_manager.upload_failed": "画像アップロードに失敗しました：@error", "anki.card_media_manager.upload_failed_no_files_uploaded": "すべてのファイルのアップロードに失敗しました", "anki.card_media_manager.upload_tab": "画像ホスティングにアップロード", "anki.card_media_manager.uploading_file": "ファイルをアップロード中：@current/@total - @filename", "anki.common.a_file": "解答ファイル", "anki.common.advanced_options": "詳細オプション", "anki.common.answer_cloze_grammar": "解答穴埋め文法", "anki.common.card_mode": "カード作成モード", "anki.common.create_subdeck": "サブデッキを作成", "anki.common.export_mode": "エクスポートモード", "anki.common.is_answer_cloze": "解答穴埋め", "anki.common.page_range": "ページ範囲", "anki.common.q_file": "問題ファイル", "anki.common.refresh_success": "更新成功", "anki.common.show_source": "出典を表示", "anki.common.tags": "タグ", "anki.common.target_deck": "対象のデッキ", "anki.deck_manager.align_bottom": "下揃え", "anki.deck_manager.align_center": "中央揃え", "anki.deck_manager.align_top": "上揃え", "anki.deck_manager.alignment": "配置方法", "anki.deck_manager.break_in_line": "行内改ページ", "anki.deck_manager.cards_count_invalid": "整数を入力してください", "anki.deck_manager.cards_count_placeholder": "カード数を入力", "anki.deck_manager.cards_count_required": "カード数を入力してください。例: 5", "anki.deck_manager.cards_per_file": "ファイル当たりのカード数", "anki.deck_manager.cards_per_row": "行当たりのカード数", "anki.deck_manager.cell_margin": "セルマージン", "anki.deck_manager.cell_margin_invalid": "正しいCSSマージン形式を入力してください。例: 10px または 5px 10px 15px 20px", "anki.deck_manager.cell_margin_placeholder": "マージンを入力。例: 10px または 5px 10px 15px 20px", "anki.deck_manager.cell_margin_required": "マージンを入力してください。例: 10px または 5px 10px 15px 20px", "anki.deck_manager.cell_padding": "セルパディング", "anki.deck_manager.cell_padding_invalid": "正しいCSSパディング形式を入力してください。例: 10px または 5px 10px 15px 20px", "anki.deck_manager.cell_padding_placeholder": "パディングを入力。例: 10px または 5px 10px 15px 20px", "anki.deck_manager.cell_padding_required": "パディングを入力してください。例: 10px または 5px 10px 15px 20px", "anki.deck_manager.clone_deck": "デッキを複製", "anki.deck_manager.create_deck": "デッキを作成", "anki.deck_manager.create_mode": "作成モード", "anki.deck_manager.create_mode_file": "ファイルインポート", "anki.deck_manager.create_mode_manual": "手動入力", "anki.deck_manager.custom_font_size": "カスタムフォントサイズ", "anki.deck_manager.deck_list": "デッキリスト", "anki.deck_manager.deck_list_placeholder": "デッキ名を入力、1行に1つのデッキ", "anki.deck_manager.deck_list_required": "デッキリストは空にできません", "anki.deck_manager.deck_names_file": "デッキ名ファイル", "anki.deck_manager.errors.export_csv_failed": "CSVエクスポートに失敗しました", "anki.deck_manager.errors.export_html_failed": "HTMLエクスポートに失敗しました: @error", "anki.deck_manager.errors.export_json_failed": "JSONエクスポートに失敗しました", "anki.deck_manager.errors.export_xlsx_failed": "Xlsxエクスポートに失敗しました", "anki.deck_manager.errors.select_deck": "デッキを選択してください", "anki.deck_manager.errors.select_deck_file": "デッキファイルを選択してください", "anki.deck_manager.errors.select_deck_to_remove": "削除するデッキを選択してください", "anki.deck_manager.errors.select_output_dir": "出力ディレクトリを選択してください", "anki.deck_manager.errors.select_source_dest_deck": "ソースデッキと宛先デッキを選択してください", "anki.deck_manager.errors.unsupported_export_mode": "サポートされていないエクスポートモード: @mode", "anki.deck_manager.export_deck": "デッキをエクスポート", "anki.deck_manager.export_format": "エクスポート形式", "anki.deck_manager.export_mode": "エクスポートモード", "anki.deck_manager.export_mode_qa_different_file": "問題と解答を分離（異なるファイル）", "anki.deck_manager.export_mode_qa_merge": "問題と解答を結合", "anki.deck_manager.export_mode_qa_same_page": "問題と解答を分離（同ページ並列）", "anki.deck_manager.export_modes.qa_different_file": "質問と回答を別ファイルに分離", "anki.deck_manager.export_modes.qa_merge": "質問と回答をマージ", "anki.deck_manager.export_modes.qa_same_page": "質問と回答を同ページに並列", "anki.deck_manager.feature_description": "デッキの一括作成、インポート、複製、エクスポートなどをサポート", "anki.deck_manager.font_size_invalid": "浮動小数点数を入力してください", "anki.deck_manager.font_size_placeholder": "フォントサイズを入力", "anki.deck_manager.font_size_required": "フォントサイズを入力してください。例: 12", "anki.deck_manager.function_description": "機能説明", "anki.deck_manager.html.answer_header": "回答", "anki.deck_manager.html.answer_style_comment": "カード @cardId 回答スタイル", "anki.deck_manager.html.answer_title": "回答 @fileIndex", "anki.deck_manager.html.card_style_comment": "カード @cardId スタイル", "anki.deck_manager.html.card_title": "カード @fileIndex", "anki.deck_manager.html.question_header": "質問", "anki.deck_manager.html.question_style_comment": "カード @cardId 質問スタイル", "anki.deck_manager.html.question_title": "質問 @fileIndex", "anki.deck_manager.import_deck": "デッキをインポート", "anki.deck_manager.include_sched": "復習進度を含む", "anki.deck_manager.messages.clone_deck_success": "デッキの複製に成功しました", "anki.deck_manager.messages.create_deck_success": "デッキの作成に成功しました", "anki.deck_manager.messages.delete_deck_success": "デッキの削除に成功しました", "anki.deck_manager.messages.delete_empty_deck_success": "空のデッキの削除に成功しました", "anki.deck_manager.messages.export_csv_success": "@count 件のノートをCSVファイルにエクスポートしました", "anki.deck_manager.messages.export_deck_success": "デッキのエクスポートに成功しました", "anki.deck_manager.messages.export_html_success": "HTMLエクスポートに成功しました", "anki.deck_manager.messages.export_json_success": "@count 件のノートをJSONファイルにエクスポートしました", "anki.deck_manager.messages.export_xlsx_success": "@count 件のノートをXlsxファイルにエクスポートしました", "anki.deck_manager.messages.generating_html": "HTMLファイルを生成中...", "anki.deck_manager.messages.generating_html_progress": "HTMLファイルを生成中 (@current/@total)", "anki.deck_manager.messages.get_media_dir_failed": "メディアディレクトリの取得に失敗しました", "anki.deck_manager.messages.import_deck_success": "デッキのインポートに成功しました", "anki.deck_manager.messages.import_from_file_success": "ファイルからデッキのインポートに成功しました", "anki.deck_manager.messages.processing": "処理中...", "anki.deck_manager.messages.querying_cards": "カードを検索中...", "anki.deck_manager.messages.sorting_cards": "カードをソート中...", "anki.deck_manager.output_directory": "出力ディレクトリ", "anki.deck_manager.remove_deck": "デッキを削除", "anki.deck_manager.remove_deck_label": "デッキを削除", "anki.deck_manager.remove_deck_placeholder": "削除するデッキを選択", "anki.deck_manager.remove_mode": "削除モード", "anki.deck_manager.remove_mode_empty": "空のデッキを削除", "anki.deck_manager.remove_mode_manual": "手動削除", "anki.deck_manager.remove_range": "削除範囲", "anki.deck_manager.remove_range_all": "すべてのデッキ", "anki.deck_manager.remove_range_part": "指定デッキ", "anki.deck_manager.sort_direction": "ソート方向", "anki.deck_manager.sort_direction_asc": "昇順", "anki.deck_manager.sort_direction_desc": "降順", "anki.deck_manager.sort_direction_random": "ランダム", "anki.deck_manager.sort_method": "ソート方法", "anki.deck_manager.sort_method_add_time": "追加時間", "anki.deck_manager.sort_method_due_time": "期限時間", "anki.deck_manager.sort_method_field": "ソートフィールド", "anki.deck_manager.sort_method_lapses": "間違い回数", "anki.deck_manager.sort_method_modify_time": "修正時間", "anki.deck_manager.sort_method_reviews": "復習回数", "anki.deck_manager.source_deck": "ソースデッキ", "anki.deck_manager.source_deck_placeholder": "ソースデッキを選択", "anki.deck_manager.source_deck_search_placeholder": "ソースデッキを入力", "anki.deck_manager.target_deck_input_placeholder": "ターゲットデッキを入力", "anki.deck_manager.target_deck_label": "ターゲットデッキ", "anki.deck_manager.target_deck_placeholder": "ターゲットデッキを入力", "anki.deck_manager.target_deck_required": "ターゲットデッキを入力してください", "anki.deck_manager.target_deck_select_placeholder": "ターゲットデッキを選択", "anki.deck_manager.title": "デッキ管理", "anki.excel_card.answer_in_column": "答えは独立した列", "anki.excel_card.answer_in_text": "答えは問題文内", "anki.excel_card.answer_pattern": "解答マッチングパターン", "anki.excel_card.answer_position": "解答位置", "anki.excel_card.choice_tab": "選択問題", "anki.excel_card.correct_answer_pattern": "正解マッチングパターン", "anki.excel_card.excel_card_title": "Excelカード作成", "anki.excel_card.excel_file": "Excelファイル", "anki.excel_card.feature_description": "Excel問答、選択、判断カード作成をサポート", "anki.excel_card.field_mapping": "フィールドマッピング", "anki.excel_card.free_match": "自由マッチング", "anki.excel_card.function_description": "機能説明", "anki.excel_card.guru_import": "Guruインポート", "anki.excel_card.id_column": "ID列名", "anki.excel_card.input_answer_pattern": "解答マッチングパターンを入力", "anki.excel_card.input_correct_answer_pattern": "正解マッチングパターンを入力", "anki.excel_card.input_option_pattern": "選択肢マッチングパターンを入力", "anki.excel_card.input_tags": "タグを入力", "anki.excel_card.input_template": "テンプレートを入力", "anki.excel_card.input_wrong_answer_pattern": "誤答マッチングパターンを入力", "anki.excel_card.judge_tab": "判断問題", "anki.excel_card.no_column_info": "Excelファイルに列情報がありません", "anki.excel_card.option_in_column": "選択肢は各列に分散", "anki.excel_card.option_in_text": "選択肢は単一列", "anki.excel_card.option_pattern": "選択肢マッチングパターン", "anki.excel_card.option_position": "選択肢位置", "anki.excel_card.qa_tab": "問答問題", "anki.excel_card.read_excel_failed": "Excelファイルの読み取りに失敗しました: @error", "anki.excel_card.select_answer_pattern": "解答マッチングパターンを選択", "anki.excel_card.select_column": "列名を選択", "anki.excel_card.select_correct_answer_pattern": "正解マッチングパターンを選択", "anki.excel_card.select_excel_file": "Excelファイルを選択", "anki.excel_card.select_excel_file_error": "Excelファイルを選択してください", "anki.excel_card.select_file_dialog": "ファイルを選択", "anki.excel_card.select_id_column_placeholder": "ID列名を選択、カードの一意IDとして使用、カード更新に使用可能", "anki.excel_card.select_option_pattern": "選択肢マッチングパターンを選択", "anki.excel_card.select_sub_deck_column": "サブデッキ列名を選択", "anki.excel_card.select_tag_column": "タグ列名を選択", "anki.excel_card.select_tags": "タグを選択", "anki.excel_card.select_template": "テンプレートを選択", "anki.excel_card.select_worksheet": "ワークシートを選択", "anki.excel_card.select_wrong_answer_pattern": "誤答マッチングパターンを選択", "anki.excel_card.sub_deck_column": "サブデッキ列名", "anki.excel_card.table_column": "テーブル列名", "anki.excel_card.tag_column": "タグ列名", "anki.excel_card.template": "テンプレート", "anki.excel_card.template_field": "テンプレートフィールド", "anki.excel_card.worksheet": "ワークシート", "anki.excel_card.wrong_answer_pattern": "誤答マッチングパターン", "anki.flash.choose_save_location_and_filename": "保存場所とファイル名を選択", "anki.flash.copy_suffix": "（コピー）", "anki.flash.editor.autoSaveFailed": "自動保存に失敗しました: @error", "anki.flash.editor.bold": "太字", "anki.flash.editor.cancel": "キャンセル", "anki.flash.editor.cannotLoadContent": "元のコンテンツを読み込めません。再編集してください。", "anki.flash.editor.codeBlock": "コードブロック", "anki.flash.editor.confirm": "確認", "anki.flash.editor.contentEmpty": "ノートの内容が空です。ノートを削除します", "anki.flash.editor.deleteEmptyNoteFailed": "空のノートの削除に失敗しました: @error", "anki.flash.editor.divider": "区切り線を追加", "anki.flash.editor.export": "エクスポート", "anki.flash.editor.exportFailed": "エクスポートに失敗しました: @error", "anki.flash.editor.exportFailedCannotSave": "エクスポートに失敗しました: ファイルを保存できません: @error", "anki.flash.editor.exportFailedEmpty": "エクスポートに失敗しました: ドキュメントの内容が空です", "anki.flash.editor.exportFailedEmptyResult": "エクスポートに失敗しました: 変換結果が空です", "anki.flash.editor.exportSuccess": "エクスポートが成功しました: ファイルが保存されました: @path", "anki.flash.editor.generatingMindMap": "マインドマップを生成中...", "anki.flash.editor.hyperlink": "ハイパーリンク", "anki.flash.editor.image": "画像を追加", "anki.flash.editor.imageDialogTitle": "画像を挿入", "anki.flash.editor.imageSelectError": "エラー: 画像を選択できません: @error", "anki.flash.editor.imageSelectLocal": "ローカル画像を選択", "anki.flash.editor.imageUrl": "画像URL", "anki.flash.editor.imageUrlPlaceholder": "https://example.com/image.jpg", "anki.flash.editor.insertImage": "画像を挿入", "anki.flash.editor.insertLink": "リンクを挿入", "anki.flash.editor.italic": "斜体", "anki.flash.editor.linkCannotOpen": "リンクを開けません: @url", "anki.flash.editor.linkDialogTitle": "リンクを挿入", "anki.flash.editor.linkOpenError": "リンクを開く際にエラーが発生しました: @error", "anki.flash.editor.linkUrl": "URL", "anki.flash.editor.linkUrlPlaceholder": "https://example.com", "anki.flash.editor.loadingImage": "画像を読み込み中...", "anki.flash.editor.mindMapCannotGenerate": "マインドマップを生成できません。Markdown形式を確認してください", "anki.flash.editor.mindMapGenerationFailed": "マインドマップの生成に失敗しました", "anki.flash.editor.mindMapMode": "マインドマップ", "anki.flash.editor.noteTitle": "ノートタイトル", "anki.flash.editor.noteTitleEmpty": "エラー：ノートタイトルは空にできません", "anki.flash.editor.orderedList": "番号付きリスト", "anki.flash.editor.placeholder": "ここでノートの内容を編集...", "anki.flash.editor.quote": "引用", "anki.flash.editor.redo": "やり直し", "anki.flash.editor.returnToEditor": "エディターに戻る", "anki.flash.editor.save": "保存", "anki.flash.editor.saveDialogTitle": "Markdownファイルを保存", "anki.flash.editor.saveFailed": "保存に失敗しました: @error", "anki.flash.editor.saveSuccess": "ノートが保存されました", "anki.flash.editor.strikethrough": "取り消し線", "anki.flash.editor.underline": "下線", "anki.flash.editor.undo": "元に戻す", "anki.flash.editor.unorderedList": "箇条書きリスト", "anki.flash.editor.untitledNote": "無題のノート", "anki.flash.errors.copy_note_index_out_of_bounds": "ノートの複製に失敗：インデックス範囲外", "anki.flash.errors.delete_note_index_out_of_bounds": "ノートの削除に失敗：インデックス範囲外", "anki.flash.errors.export_note_index_out_of_bounds": "ノートのエクスポートに失敗：インデックス範囲外", "anki.flash.errors.export_to_anki_failed": "Ankiへのノートエクスポートに失敗しました", "anki.flash.errors.export_to_markdown_failed": "Markdownへのノートエクスポートに失敗しました", "anki.flash.errors.load_notes_failed": "フラッシュノートの読み込みに失敗しました", "anki.flash.errors.save_notes_failed": "フラッシュノートの保存に失敗しました", "anki.flash.errors.update_note_index_out_of_bounds": "ノートの更新に失敗：インデックス範囲外", "anki.flash.example_note": "サンプルノート", "anki.flash.example_note_content": "これはサンプルノートです。編集をクリックするか、新しいノートを作成できます。\n\nフラッシュノートは、アイデアやひらめきを素早く記録するために使用できます。", "anki.flash.example_note_preview": "これはサンプルノートです。編集をクリックするか、新しいノートを作成できます。", "anki.flash.export_failed": "エクスポートに失敗しました: @error", "anki.flash.export_feature_under_development": "エクスポート機能は開発中です...", "anki.flash.messages.exported_to_anki": "ノートをAnkiにエクスポートしました", "anki.flash.messages.exported_to_markdown": "ノートをMarkdownにエクスポートしました", "anki.flash.messages.loaded_notes": "@count 件のフラッシュノートを読み込みました", "anki.flash.messages.saved_notes": "@count 件のフラッシュノートを保存しました", "anki.flash.messages.user_cancelled_export": "ユーザーがエクスポート操作をキャンセルしました", "anki.flash.new_note": "新しいノート", "anki.flash.note_exported_to": "ノートがエクスポートされました: @path", "anki.flash.untitled_note": "無題のノート", "anki.image_card.auto_ocr": "自動OCR", "anki.image_card.cancel": "キャンセル", "anki.image_card.cards_added_to_anki": "@count枚のカードがAnkiに追加されました", "anki.image_card.click_or_drag_to_select": "クリックして画像を選択、または画像をここにドラッグ", "anki.image_card.click_to_select": "クリックして画像を選択", "anki.image_card.complete_and_return": "完了して戻る", "anki.image_card.config_title": "設定", "anki.image_card.deck_name_required": "デッキ名は空にできません", "anki.image_card.default_cloze_mode": "デフォルト穴埋めモード", "anki.image_card.default_cloze_mode_placeholder": "デフォルト穴埋めモードを選択", "anki.image_card.default_deck": "デフォルトデッキ", "anki.image_card.default_deck_placeholder": "デフォルトデッキ名を入力", "anki.image_card.default_tags": "デフォルトタグ", "anki.image_card.default_tags_placeholder": "デフォルトタグを入力、カンマで区切る", "anki.image_card.delete": "削除", "anki.image_card.delete_all_confirmation": "すべての画像を削除しますか？", "anki.image_card.delete_all_masks": "すべてのマスクを削除", "anki.image_card.delete_all_masks_confirmation": "すべてのマスクを削除してもよろしいですか？", "anki.image_card.delete_all_masks_title": "すべてのマスクを削除", "anki.image_card.delete_image_title": "画像を削除", "anki.image_card.failed": "失敗", "anki.image_card.failed_to_read_image_from_clipboard": "クリップボードから画像の読み取りに失敗しました", "anki.image_card.file_format_error": ".png, .jpg, .jpeg, .gif, .bmp形式のファイルを選択してください", "anki.image_card.file_selection_failed": "ファイル選択に失敗しました", "anki.image_card.free_guess": "自由推測", "anki.image_card.generate_card": "", "anki.image_card.group_mode_title": "グループモード", "anki.image_card.guru_import": "Guruインポート", "anki.image_card.image_added_from_capture": "スクリーンショットをキャプチャして追加しました", "anki.image_card.image_added_from_clipboard": "クリップボードから画像を追加しました", "anki.image_card.image_cloze": "画像穴埋め", "anki.image_card.images_selected": "@count枚の画像を選択しました", "anki.image_card.mask_all_guess_all": "全隠し全推測", "anki.image_card.mask_all_guess_one": "全隠し単一推測", "anki.image_card.mask_one_guess_one": "単一隠し単一推測", "anki.image_card.masks_deleted_count": "@count 個のマスクを削除しました", "anki.image_card.masks_deleted_success": "削除成功", "anki.image_card.next_image": "次の画像", "anki.image_card.no_image_selected": "画像が選択されていません", "anki.image_card.no_masks_to_delete": "削除するマスクがありません", "anki.image_card.not_set": "未設定", "anki.image_card.note_label": "ノート", "anki.image_card.note_placeholder": "ノートを追加", "anki.image_card.one_cloze_per_card": "1穴1カード", "anki.image_card.page_indicator": "@current/@total枚目", "anki.image_card.paste_hotkey": "画像貼り付けホットキー", "anki.image_card.please_add_images_first": "まず画像を追加してください", "anki.image_card.previous_image": "前の画像", "anki.image_card.primary_color": "マスク色（メイン）", "anki.image_card.redo": "やり直し", "anki.image_card.scratch_guess": "スクラッチ推測", "anki.image_card.secondary_color": "マスク色（サブ）", "anki.image_card.settings_saved": "設定が保存されました", "anki.image_card.snap_hotkey": "スクリーンショットホットキー", "anki.image_card.submit": "送信", "anki.image_card.success": "成功", "anki.image_card.tags_label": "タグ", "anki.image_card.tags_placeholder": "タグを入力、カンマで区切る", "anki.image_card.title": "画像カード作成", "anki.image_card.undo": "元に戻す", "anki.llm_card.add_divider": "区切り線を追加", "anki.llm_card.add_image": "画像を追加", "anki.llm_card.advanced_settings": "詳細設定", "anki.llm_card.ai_card_title": "AIカード作成", "anki.llm_card.answer_cloze": "解答穴埋め", "anki.llm_card.api_address": "APIアドレス", "anki.llm_card.api_key": "APIキー", "anki.llm_card.api_key_cannot_empty": "APIキーは空にできません", "anki.llm_card.at_least_one_card_type": "少なくとも1つの生成問題タイプを選択してください", "anki.llm_card.at_least_one_difficulty": "少なくとも1つの問題難易度を選択してください", "anki.llm_card.auto_save_failed": "自動保存に失敗しました: @error", "anki.llm_card.bold": "太字", "anki.llm_card.cancel": "キャンセル", "anki.llm_card.cannot_open_link": "リンクを開けません: @url", "anki.llm_card.character_unit": "文字", "anki.llm_card.choice_card": "単一選択", "anki.llm_card.chunk_size": "テキストチャンクサイズ", "anki.llm_card.chunk_size_cannot_empty": "テキストチャンクサイズは空にできません", "anki.llm_card.chunk_size_range_error": "テキストチャンクサイズは0より大きい必要があります", "anki.llm_card.cloze_card": "穴埋め", "anki.llm_card.code_block": "コードブロック", "anki.llm_card.confirm": "確定", "anki.llm_card.confirm_delete": "削除を確認", "anki.llm_card.copy_success": "コピーに成功しました", "anki.llm_card.copy_suffix": "コピー", "anki.llm_card.copy_suffix_numbered": "コピー @number", "anki.llm_card.custom": "カスタム", "anki.llm_card.custom_model": "カスタムモデル", "anki.llm_card.custom_model_required": "カスタムモデルを使用する場合、モデル名とAPIエンドポイントを入力してください", "anki.llm_card.default_cloze_mode": "デフォルト穴埋めモード", "anki.llm_card.delete": "削除", "anki.llm_card.delete_prompt_confirmation": "プロンプト「@name」を削除してもよろしいですか？", "anki.llm_card.delete_success": "削除に成功しました", "anki.llm_card.difficulty_advanced": "上級", "anki.llm_card.difficulty_basic": "基礎", "anki.llm_card.difficulty_medium": "中級", "anki.llm_card.difficulty_none": "なし", "anki.llm_card.error": "エラー", "anki.llm_card.feature_description": "AI大規模言語モデルを呼び出してカードを自動生成することをサポート", "anki.llm_card.free_guess": "自由推測", "anki.llm_card.function_description": "機能説明", "anki.llm_card.generate_card_type": "生成問題タイプ", "anki.llm_card.generate_prompt": "プロンプトを生成", "anki.llm_card.generate_prompt_failed": "プロンプト生成に失敗しました: @error", "anki.llm_card.get_prompt_from_storage": "ストレージからプロンプトを取得: @name", "anki.llm_card.guru_import": "Guruインポート", "anki.llm_card.guru_official": "Guru公式", "anki.llm_card.html_escape": "HTMLエスケープ", "anki.llm_card.hyperlink": "ハイパーリンク", "anki.llm_card.image_url": "画像URL", "anki.llm_card.input_api_address_placeholder": "APIアドレスURLを入力、例: https://example.com/v1", "anki.llm_card.input_api_key_placeholder": "APIキーを入力してください", "anki.llm_card.input_chunk_size_placeholder": "テキストチャンクサイズを入力", "anki.llm_card.input_custom_model_name": "カスタムモデル名を入力", "anki.llm_card.input_max_concurrent_requests_placeholder": "最大同時リクエスト数を入力", "anki.llm_card.input_max_tokens_placeholder": "最大出力トークン数を入力", "anki.llm_card.input_model": "入力モデル", "anki.llm_card.input_page_range_placeholder": "ページ範囲を入力、空欄は全ページ", "anki.llm_card.input_prompt_title_placeholder": "プロンプトタイトルを入力、例: 問答カードプロンプト", "anki.llm_card.input_tags": "タグを入力", "anki.llm_card.input_temperature_placeholder": "温度を入力、0-2の間", "anki.llm_card.input_timeout_placeholder": "タイムアウトを入力", "anki.llm_card.input_top_k_placeholder": "Top Kを入力", "anki.llm_card.input_top_p_placeholder": "Top Pを入力", "anki.llm_card.insert_image": "画像を挿入", "anki.llm_card.insert_link": "リンクを挿入", "anki.llm_card.invalid_number": "有効な数値を入力してください", "anki.llm_card.italic": "斜体", "anki.llm_card.judge_card": "判断", "anki.llm_card.log_directory": "ログディレクトリ", "anki.llm_card.mask_all_guess_all": "全隠し全推測", "anki.llm_card.mask_all_guess_one": "全隠し単一推測", "anki.llm_card.mask_one_guess_one": "単一隠し単一推測", "anki.llm_card.max_concurrent_requests": "最大同時リクエスト数", "anki.llm_card.max_concurrent_requests_cannot_empty": "最大同時リクエスト数は空にできません", "anki.llm_card.max_concurrent_requests_range_error": "最大同時リクエスト数は0より大きい必要があります", "anki.llm_card.max_tokens": "最大出力トークン数", "anki.llm_card.model_provider": "モデルプロバイダー", "anki.llm_card.multi_choice_card": "複数選択", "anki.llm_card.my_prompts": "マイプロンプト", "anki.llm_card.no_prompts_message": "プロンプトがありません。まず作成してください", "anki.llm_card.one_cloze_per_card": "1穴1カード", "anki.llm_card.open_link_error": "リンクを開く際にエラーが発生しました: @error", "anki.llm_card.ordered_list": "順序付きリスト", "anki.llm_card.page_range": "ページ範囲", "anki.llm_card.parse_prompt_json_failed": "プロンプトJSONの解析に失敗しました: @error", "anki.llm_card.prompt_copied": "プロンプトがコピーされました", "anki.llm_card.prompt_deleted": "プロンプトが削除されました", "anki.llm_card.prompt_management_tab": "プロンプト管理", "anki.llm_card.prompt_name_cannot_empty": "プロンプト名は空にできません", "anki.llm_card.prompt_name_exists_renamed": "プロンプト名「@name」は既に存在します。自動的に「@uniqueName」に名前を変更しました", "anki.llm_card.prompt_saved": "プロンプトが保存されました", "anki.llm_card.prompt_selection_changed": "プロンプト選択が変更されました。プロンプトリストを再読み込み: @value", "anki.llm_card.protocol_type": "プロトコルタイプ", "anki.llm_card.qa_card": "問答", "anki.llm_card.question_difficulty": "問題難易度", "anki.llm_card.quote": "引用", "anki.llm_card.reasoning_model": "推論モデル", "anki.llm_card.save": "保存", "anki.llm_card.select_at_least_one_card_type": "少なくとも1つのカードタイプを選択してください", "anki.llm_card.select_at_least_one_file": "カード作成素材として少なくとも1つのファイルを選択してください", "anki.llm_card.select_local_image": "ローカル画像を選択", "anki.llm_card.select_model": "モデルを選択", "anki.llm_card.select_model_provider": "モデルプロバイダーを選択", "anki.llm_card.select_prompt": "", "anki.llm_card.select_protocol_type": "プロトコルタイプを選択", "anki.llm_card.select_system_prompt": "システムプロンプトを選択", "anki.llm_card.select_tags": "タグを選択", "anki.llm_card.smart_card_tab": "スマートカード作成", "anki.llm_card.sort_by_create_time": "作成時間順", "anki.llm_card.sort_by_modify_time": "修正時間順", "anki.llm_card.sort_by_name": "名前順", "anki.llm_card.sort_by_word_count": "文字数順", "anki.llm_card.strikethrough": "取り消し線", "anki.llm_card.success": "成功", "anki.llm_card.system_default": "システムデフォルト", "anki.llm_card.system_prompt": "システムプロンプト", "anki.llm_card.temperature": "温度", "anki.llm_card.temperature_cannot_empty": "温度は空にできません", "anki.llm_card.temperature_range_error": "温度は0-2の間である必要があります", "anki.llm_card.test": "テスト", "anki.llm_card.timeout_cannot_empty": "タイムアウトは空にできません", "anki.llm_card.timeout_seconds": "タイムアウト（秒）", "anki.llm_card.title": "タイトル", "anki.llm_card.title_cannot_empty": "タイトルは空にできません", "anki.llm_card.tokens_cannot_empty": "トークン数は空にできません", "anki.llm_card.top_k": "Top K", "anki.llm_card.top_k_cannot_empty": "Top Kは空にできません", "anki.llm_card.top_p": "Top P", "anki.llm_card.top_p_cannot_empty": "Top Pは空にできません", "anki.llm_card.top_p_range_error": "Top Pは0-1の間である必要があります", "anki.llm_card.unable_to_load_content": "元のコンテンツを読み込めません。再編集してください。", "anki.llm_card.underline": "下線", "anki.llm_card.unnamed_prompt": "無名プロンプト", "anki.llm_card.unordered_list": "順序なしリスト", "anki.llm_card.update_prompt_name_exists_renamed": "更新されたプロンプト名「@name」は既に存在します。自動的に「@uniqueName」に名前を変更しました", "anki.llm_card.use_ai_tags": "", "anki.markdown_card.answer_cloze": "解答穴埋め", "anki.markdown_card.at_least_one_cloze_grammar": "少なくとも1つの穴埋め文法を選択してください", "anki.markdown_card.chinese_character_set": "中国語文字セット", "anki.markdown_card.cloze_grammar": "穴埋め文法", "anki.markdown_card.cloze_tab": "穴埋め問題", "anki.markdown_card.feature_description": "Markdownノートのカード作成をサポート、問答・穴埋めカード作成をサポート", "anki.markdown_card.free_guess": "自由推測", "anki.markdown_card.function_description": "機能説明", "anki.markdown_card.guru_import": "Guruインポート", "anki.markdown_card.ignore_group": "グループを無視", "anki.markdown_card.input_tags": "タグを入力", "anki.markdown_card.mask_all_guess_all": "全隠し全推測", "anki.markdown_card.mask_all_guess_one": "全隠し単一推測", "anki.markdown_card.mask_one_guess_one": "単一隠し単一推測", "anki.markdown_card.media_file_directory": "メディアファイルディレクトリ", "anki.markdown_card.media_folder_placeholder": "メディアフォルダ、空欄の場合はファイルと同じディレクトリを使用", "anki.markdown_card.none": "なし", "anki.markdown_card.obsidian_syntax": "Obsidian構文", "anki.markdown_card.one_cloze_per_card": "1穴1カード", "anki.markdown_card.please_select_files": "ファイルを選択してください！", "anki.markdown_card.please_select_kevin_text_cloze_template": "【Kevin Text Cloze v3】テンプレートを選択してください", "anki.markdown_card.please_set_front_field_pattern": "Frontフィールドのマッチングパターンを設定してください！", "anki.markdown_card.processing_question": "@current問目を処理中", "anki.markdown_card.qa_tab": "問答問題", "anki.markdown_card.select_tags": "タグを選択", "anki.markdown_card.separator": "区切り文字", "anki.markdown_card.title": "Markdownカード作成", "anki.media_card.audio": "音声", "anki.media_card.card_template": "カードテンプレート", "anki.media_card.end_time": "終了時間", "anki.media_card.end_time_error": "正しい終了時間を入力してください。例: 00:00:00", "anki.media_card.end_time_placeholder": "終了時間を入力。例: 00:00:00", "anki.media_card.feature_description": "テキスト、画像、音声、動画などのマルチメディアカード作成をサポート", "anki.media_card.ffmpeg_processing_failed": "FFmpeg処理に失敗しました: @error", "anki.media_card.field_mapping": "フィールドマッピング", "anki.media_card.file_content": "ファイル内容", "anki.media_card.file_suffix": "ファイル拡張子", "anki.media_card.filename": "ファイル名", "anki.media_card.function_description": "機能説明", "anki.media_card.guru_import": "Guruインポート", "anki.media_card.image": "画像", "anki.media_card.input_file_not_exist": "入力ファイルが存在しません: @path", "anki.media_card.input_file_suffix": "入力ファイル拡張子", "anki.media_card.input_tags": "タグを入力", "anki.media_card.left_padding_error": "正しい左パディングを入力してください。例: 200", "anki.media_card.left_padding_ms": "左パディング（ミリ秒）", "anki.media_card.left_padding_placeholder": "左パディングを入力、単位: ミリ秒", "anki.media_card.match_type": "マッチングタイプ", "anki.media_card.media_card_title": "マルチメディアカード作成", "anki.media_card.media_directory_not_exist": "メディアディレクトリが存在しません", "anki.media_card.media_file": "メディアファイル", "anki.media_card.media_file_directory": "メディアファイルディレクトリ", "anki.media_card.media_file_directory_placeholder": "メディアファイルディレクトリを入力してください", "anki.media_card.no_matching_files_found": "マッチする拡張子のファイルが見つかりません", "anki.media_card.no_valid_cards_generated": "有効なカードが生成されませんでした", "anki.media_card.none": "なし", "anki.media_card.operation_failed": "操作に失敗しました: @error", "anki.media_card.please_configure_ffmpeg_path": "設定でffmpegパスを設定してください", "anki.media_card.please_select_file_suffix": "ファイル拡張子を選択してください", "anki.media_card.please_select_media_directory": "メディアファイルディレクトリを選択してください", "anki.media_card.please_select_subtitle_and_media_files": "字幕ファイルとメディアファイルを選択してください", "anki.media_card.processing": "処理中...", "anki.media_card.processing_file": "ファイルを処理中: @filename", "anki.media_card.qa_card_tab": "問答カード作成", "anki.media_card.right_padding_error": "正しい右パディングを入力してください。例: 200", "anki.media_card.right_padding_ms": "右パディング（ミリ秒）", "anki.media_card.right_padding_placeholder": "右パディングを入力、単位: ミリ秒", "anki.media_card.select_card_template": "カードテンプレートを選択", "anki.media_card.select_file_suffix": "ファイル拡張子を選択", "anki.media_card.select_match_type": "マッチングタイプを選択", "anki.media_card.select_tags": "タグを選択", "anki.media_card.start_time": "開始時間", "anki.media_card.start_time_error": "正しい開始時間を入力してください。例: 00:00:00", "anki.media_card.start_time_placeholder": "開始時間を入力。例: 00:00:00", "anki.media_card.subtitle_card_tab": "字幕カード作成", "anki.media_card.subtitle_file": "字幕ファイル", "anki.media_card.subtitle_offset_error": "正しい字幕オフセットを入力してください。例: 200", "anki.media_card.subtitle_offset_ms": "字幕オフセット（ミリ秒）", "anki.media_card.subtitle_offset_placeholder": "字幕オフセットを入力、単位: ミリ秒", "anki.media_card.template_field": "テンプレートフィールド", "anki.media_card.text": "テキスト", "anki.media_card.video": "動画", "anki.media_card.video_output_format": "動画出力形式", "anki.mindmap.at_least_one_cloze_style": "少なくとも1つの穴埋めスタイルを選択してください", "anki.mindmap.blue_font": "青文字", "anki.mindmap.blue_highlight": "青ハイライト", "anki.mindmap.blue_underline": "青下線", "anki.mindmap.cloze_mode": "穴埋めモード", "anki.mindmap.cloze_style": "穴埋めスタイル", "anki.mindmap.feature_description": "Xmind、知犀、幕布、Markdownなど多種類のソースからのカード作成をサポート、階層制限なし、穴埋め・問答カード作成をサポート", "anki.mindmap.green_font": "緑文字", "anki.mindmap.green_highlight": "緑ハイライト", "anki.mindmap.green_underline": "緑下線", "anki.mindmap.highlight_color": "ハイライト色", "anki.mindmap.input_color_placeholder": "色を入力、例: #FF0000", "anki.mindmap.input_file_required": "入力ファイルは空にできません！", "anki.mindmap.map_id_placeholder": "MapIDを入力、カード更新時に使用", "anki.mindmap.media_file_directory": "メディアファイルディレクトリ", "anki.mindmap.media_folder_placeholder": "メディアフォルダ、空欄の場合はファイルと同じディレクトリを使用", "anki.mindmap.mindmap_card_title": "マインドマップカード作成", "anki.mindmap.mindmap_source": "マインドマップソース", "anki.mindmap.obsidian_syntax": "Obsidian構文", "anki.mindmap.red_font": "赤文字", "anki.mindmap.red_highlight": "赤ハイライト", "anki.mindmap.red_underline": "赤下線", "anki.mindmap.select_color": "色を選択または入力", "anki.mindmap.source_mubu": "幕布", "anki.mindmap.source_zhixi": "知犀", "anki.mindmap.strikeout": "取り消し線", "anki.mindmap.text_color": "テキスト色", "anki.mindmap.text_highlight_short": "テキストハイライト", "anki.mindmap.yellow_highlight": "黄ハイライト", "anki.mubu.answer_cloze": "回答穴埋め", "anki.mubu.back": "裏面(システム組み込み)", "anki.mubu.blue": "青(blue)", "anki.mubu.bold": "太字", "anki.mubu.cannot_extract_token": "Cookieからjwt-tokenを抽出できません。Cookieが有効か確認してください", "anki.mubu.card_generation_complete": "カード生成完了", "anki.mubu.card_template": "カードテンプレート", "anki.mubu.card_type": "カード問題タイプ", "anki.mubu.cloze": "穴埋め問題", "anki.mubu.cloze_mode": "穴埋めモード", "anki.mubu.cloze_style": "穴埋めスタイル", "anki.mubu.connection_timeout": "接続タイムアウト、ネットワークを確認してください", "anki.mubu.cookie": "<PERSON><PERSON>", "anki.mubu.cookie_cannot_be_empty": "<PERSON><PERSON>は空にできません", "anki.mubu.cookie_validation_error": "検証プロセスでエラーが発生しました", "anki.mubu.cookie_validation_failed": "Cookie検証失敗", "anki.mubu.create_time": "作成時間", "anki.mubu.cyan": "シアン(cyan)", "anki.mubu.document_data_empty": "ドキュメントデータが空で、構造を解析できません", "anki.mubu.document_definition_empty": "ドキュメント定義が空で、構造を解析できません", "anki.mubu.document_id": "ドキュメントID", "anki.mubu.document_no_content": "ドキュメントに内容がないか解析に失敗しました", "anki.mubu.export_failed": "エクスポートに失敗しました", "anki.mubu.feature_description": "幕布ノートのカード作成をサポート、問答・穴埋めカード作成をサポート、幕布ノートの一括エクスポートもサポート", "anki.mubu.free_guess": "自由推測", "anki.mubu.front": "表面(システム組み込み)", "anki.mubu.front_field_mapping_error": "1つのフィールドを表面内容（Front）として設定してください", "anki.mubu.function_description_title": "機能説明", "anki.mubu.green": "緑(green)", "anki.mubu.grey": "灰色(grey)", "anki.mubu.height": "高さ", "anki.mubu.hierarchy": "親子ノード型", "anki.mubu.importing_cards": "カードをインポート中...", "anki.mubu.input_cookie": "Cookieを入力", "anki.mubu.input_document": "ドキュメントを入力", "anki.mubu.input_tags": "タグを入力", "anki.mubu.italic": "斜体", "anki.mubu.item_count": "項目数", "anki.mubu.level_1": "レベル1見出し", "anki.mubu.level_2": "レベル2見出し", "anki.mubu.level_3": "レベル3見出し", "anki.mubu.level_4": "レベル4見出し", "anki.mubu.level_5": "レベル5見出し", "anki.mubu.level_6": "レベル6見出し", "anki.mubu.mask_all_guess_all": "全隠し全推測", "anki.mubu.mask_all_guess_one": "全隠し単一推測", "anki.mubu.mask_one_guess_one": "単一隠し単一推測", "anki.mubu.mindmap": "完全マインドマップ型", "anki.mubu.network_error": "ネットワークエラー", "anki.mubu.no_auth_info": "認証情報が設定されていません。先にsetCookie()を呼び出してください", "anki.mubu.node_desc": "ノード-説明型", "anki.mubu.none": "なし", "anki.mubu.olive": "オリーブ(olive)", "anki.mubu.one_cloze_per_card": "1穴1カード", "anki.mubu.page_title": "幕布カード作成", "anki.mubu.parse_document_definition_json_failed": "ドキュメント定義JSONの解析に失敗", "anki.mubu.part_card": "部分カード作成", "anki.mubu.pink": "ピンク(pink)", "anki.mubu.processing_node": "ノードを処理中", "anki.mubu.purple": "紫(purple)", "anki.mubu.q_node_level": "質問ノードレベル", "anki.mubu.qa": "Q&A問題", "anki.mubu.receive_timeout": "応答受信タイムアウト、ネットワークを確認してください", "anki.mubu.red": "赤(red)", "anki.mubu.request_canceled": "リクエストはキャンセルされました", "anki.mubu.select_card_template": "カードテンプレートを選択", "anki.mubu.select_cloze_style": "少なくとも1つの穴埋めスタイルを選択してください", "anki.mubu.select_q_node_level": "質問ノードレベルを選択", "anki.mubu.select_tags": "タグを選択", "anki.mubu.select_target_document": "ターゲットドキュメントを選択", "anki.mubu.select_text_color": "色を選択", "anki.mubu.select_text_highlight": "色を選択", "anki.mubu.send_timeout": "リクエスト送信タイムアウト、ネットワークを確認してください", "anki.mubu.sep": "---", "anki.mubu.server_error": "サーバーエラー", "anki.mubu.show_source": "出典を表示", "anki.mubu.strikeout": "取り消し線", "anki.mubu.tab_card_creation": "ノートカード作成", "anki.mubu.tab_note_export": "ノートエクスポート", "anki.mubu.target_document": "ターゲットドキュメント", "anki.mubu.text_color": "テキスト色", "anki.mubu.text_highlight": "ハイライト色", "anki.mubu.underline": "下線", "anki.mubu.unknow_error": "不明なエラーが発生しました", "anki.mubu.update_time": "更新時間", "anki.mubu.view_source": "出典を表示", "anki.mubu.width": "幅", "anki.mubu.yellow": "黄(yellow)", "anki.ocr.all_text": "すべてのテキスト", "anki.ocr.and_other_errors": "および他@count個のエラー", "anki.ocr.cancel": "キャンセル", "anki.ocr.cannot_convert_image_to_png": "画像をPNG形式に変換できません", "anki.ocr.cannot_process_any_ocr_results": "OCR結果を処理できません", "anki.ocr.cannot_save_any_valid_image_files": "有効な画像ファイルを保存できません", "anki.ocr.card_id": "カードID", "anki.ocr.card_id_cannot_empty": "カードIDは空にできません", "anki.ocr.card_id_illegal": "カードIDが存在しないか無効です。例：1736773785607,1736773785608", "anki.ocr.card_id_placeholder": "カードIDリストを入力してください。カンマ区切り、例：1736080247115,1736080247119", "anki.ocr.card_ocr_description": "Ankiカード内の画像にOCRを実行してフィールド内容を更新", "anki.ocr.card_ocr_tab": "カードOCR", "anki.ocr.card_ocr_title": "カードOCR", "anki.ocr.card_template": "カードテンプレート", "anki.ocr.clickToSelectImage": "クリックして画像を選択", "anki.ocr.clickToSelectOrDragImage": "クリックして画像を選択、またはここに画像をドラッグ", "anki.ocr.clipboard_read_failed": "クリップボードから画像の読み取りに失敗しました", "anki.ocr.copied": "コピーしました", "anki.ocr.copied_to_clipboard": "クリップボードにコピーしました", "anki.ocr.copy": "コピー", "anki.ocr.custom": "カスタム", "anki.ocr.defaultOcrProvider": "デフォルトOCRプロバイダー", "anki.ocr.delete": "削除", "anki.ocr.deleteAllImages": "すべての画像を削除しますか？", "anki.ocr.deleteImage": "画像を削除", "anki.ocr.error_processing_single_image": "単一画像の処理中にエラーが発生しました: @error", "anki.ocr.error_saving_image_files": "画像ファイルの保存中にエラーが発生しました: @error", "anki.ocr.export_failed": "エクスポートに失敗しました: @error", "anki.ocr.exported_to": "エクスポートしました: @path", "anki.ocr.failed_to_recognize_any_images": "画像の認識に成功しませんでした", "anki.ocr.feature_description": "画像またはAnkiカード内の画像にOCR文字認識を実行", "anki.ocr.feature_not_supported_on_current_system": "この機能は現在のシステムではサポートされていません", "anki.ocr.field_config": "フィールド設定", "anki.ocr.file_selection_failed": "ファイル選択に失敗しました", "anki.ocr.fill_field": "フィールドを埋める", "anki.ocr.imageDeleted": "画像が削除されたか存在しません", "anki.ocr.imageOcr": "画像OCR", "anki.ocr.image_data_invalid": "画像データが無効です: @path", "anki.ocr.image_file_not_exist": "画像ファイルが存在しません: @path", "anki.ocr.image_ocr_tab": "画像OCR", "anki.ocr.merge_output": "出力を結合", "anki.ocr.noImageSelected": "画像が選択されていません", "anki.ocr.no_valid_image_in_clipboard": "クリップボードに有効な画像がありません", "anki.ocr.ocr_engine_initialization_failed": "OCRエンジンの初期化に失敗しました。アプリの再起動またはアプリバージョンの更新をお試しください", "anki.ocr.ocr_model_file_invalid": "OCRモデルファイルが無効です: @fileName", "anki.ocr.ocr_processing_failed": "OCR処理に失敗しました: @error", "anki.ocr.ocr_recognition_completed": "OCR認識完了", "anki.ocr.ocr_recognition_failed": "OCR認識に失敗しました: @error", "anki.ocr.ocr_recognition_partially_completed": "OCR認識が部分的に完了しました: @success/@total枚の画像が処理されました", "anki.ocr.ocr_service_returned_empty_result": "OCRサービスが空の結果を返しました", "anki.ocr.original_field": "元のフィールド", "anki.ocr.pageIndicator": "@current/@total 枚目", "anki.ocr.partial_ocr_recognition_failed": "一部の画像のOCR認識に失敗しました。残りの画像を処理します", "anki.ocr.pleaseSelectValidImageFormat": ".png, .jpg, .jpeg形式のファイルを選択してください", "anki.ocr.please_add_images_first": "まず画像を追加してください", "anki.ocr.please_select_cards_first": "まずカードを選択してください", "anki.ocr.processing_note": "ノートを処理中: @noteId", "anki.ocr.recognizing": "認識中...", "anki.ocr.save_ocr_text": "OCRテキストを保存", "anki.ocr.selectOcrProvider": "OCRプロバイダーを選択", "anki.ocr.select_card_template": "カードテンプレートを選択", "anki.ocr.select_field": "フィールドを選択", "anki.ocr.selectedImages": "@count 枚の画像を選択済み", "anki.ocr.selected_text": "選択されたテキスト", "anki.ocr.settings": "設定", "anki.ocr.submit": "送信", "anki.ocr.table_ocr_col": "OCR認識", "anki.ocr.table_replace_col": "置換内容", "anki.ocr.text_copied_to_clipboard": "テキストがクリップボードにコピーされました", "anki.ocr.title": "OCR認識", "anki.pdf_card.a_item_id": "解答項目ID", "anki.pdf_card.a_page_range": "解答ファイルページ範囲", "anki.pdf_card.annot_type_note": "付箋", "anki.pdf_card.annot_type_text": "テキストボックス", "anki.pdf_card.annotation_types": "注釈タイプ", "anki.pdf_card.cloze_mode_free_guess": "自由推測", "anki.pdf_card.cloze_mode_mask_all_guess_all": "全隠し全推測", "anki.pdf_card.cloze_mode_mask_all_guess_one": "全隠し単一推測", "anki.pdf_card.cloze_mode_mask_one_guess_one": "単一隠し単一推測", "anki.pdf_card.cloze_mode_scratch_guess": "スクラッチ推測", "anki.pdf_card.cloze_tab": "穴埋めカード", "anki.pdf_card.exporting_item": "項目をエクスポート中...", "anki.pdf_card.extra_info": "追加情報", "anki.pdf_card.feature_description": "穴埋めカード、問答カード作成をサポート、複数のカード作成モードをサポート", "anki.pdf_card.full_page_cloze": "全ページ穴埋め", "anki.pdf_card.function_description": "機能説明", "anki.pdf_card.getting_item_info": "項目情報を取得中...", "anki.pdf_card.guru_import": "Guruインポート", "anki.pdf_card.image_occlusion_builtin": "Image Occlusion（Anki内蔵）", "anki.pdf_card.invalid_answer_item_id": "無効な解答項目ID: @itemId", "anki.pdf_card.invalid_item_id": "無効な項目ID: @itemId", "anki.pdf_card.invalid_question_item_id": "無効な問題項目ID: @itemId", "anki.pdf_card.main_color": "メインカラー", "anki.pdf_card.mask_color": "マスクカラー", "anki.pdf_card.mask_type": "マスクタイプ", "anki.pdf_card.mask_type_highlight": "ハイライト", "anki.pdf_card.mask_type_square": "矩形", "anki.pdf_card.mask_type_squiggly": "波線", "anki.pdf_card.mask_type_strikeout": "取り消し線", "anki.pdf_card.mask_type_underline": "下線", "anki.pdf_card.mix_card": "混合カード作成", "anki.pdf_card.one_cloze_per_card": "1穴1カード", "anki.pdf_card.pdf_columns": "PDF列数", "anki.pdf_card.please_select_file": "ファイルを選択してください", "anki.pdf_card.q_item_id": "問題項目ID", "anki.pdf_card.q_page_range": "問題ファイル", "anki.pdf_card.qa_mode_dual_file": "問題解答分離", "anki.pdf_card.qa_mode_dual_page": "両ページカード作成", "anki.pdf_card.qa_mode_note": "ノートカード作成", "anki.pdf_card.qa_mode_single_file": "問題解答連続", "anki.pdf_card.qa_mode_single_page": "単一ページカード作成", "anki.pdf_card.qa_tab": "問答カード", "anki.pdf_card.second_color": "サブカラー", "anki.pdf_card.title": "PDFカード作成", "anki.pdf_card.zotero_card": "Zoteroカード作成", "anki.pdf_card.zotero_item_id": "項目ID", "anki.pdf_card.zotero_windows_mac_only": "Zoteroカード作成機能はWindows、Macシステムのみサポート", "anki.pdf_note.actions.failed": "失敗", "anki.pdf_note.actions.hint": "ヒント", "anki.pdf_note.actions.in_development": "開発中", "anki.pdf_note.actions.save": "保存", "anki.pdf_note.actions.success": "成功", "anki.pdf_note.config.auto_paste": "自動貼り付け", "anki.pdf_note.config.custom_link_format": "カスタムリンク形式", "anki.pdf_note.config.custom_link_format_placeholder": "カスタムリンク形式を入力してください、例: [{{title}}](<{{url}}>)", "anki.pdf_note.config.link_format": "リンク形式", "anki.pdf_note.config.link_protocol": "ジャンププロトコル", "anki.pdf_note.content.image": "画像", "anki.pdf_note.content.view_original": "[原文を表示]", "anki.pdf_note.description.function_title": "機能説明", "anki.pdf_note.description.main": "PDFを読みながらメモを取り、PDFページのバックリンクを素早く挿入でき、クリックすると元の場所に直接ジャンプできます。Obsidian、Logseq、Feishu、Yuque、Notion、Word、OneNoteなど多くのメモアプリに対応", "anki.pdf_note.description.zotero_notice": "注意：この機能はZoteroと組み合わせて使用する必要があります", "anki.pdf_note.errors.custom_link_format_empty": "カスタムリンク形式は空にできません", "anki.pdf_note.errors.get_item_notes_failed": "項目ノートの取得に失敗しました", "anki.pdf_note.errors.get_pdf_info_failed": "PDF情報の取得に失敗しました", "anki.pdf_note.errors.get_selected_annotation_failed": "選択された注釈の取得に失敗しました", "anki.pdf_note.errors.image_data_empty": "画像データが空です", "anki.pdf_note.errors.no_bookmarks_in_pdf": "PDFにブックマークがありません", "anki.pdf_note.errors.no_selected_annotation": "選択された注釈がありません", "anki.pdf_note.errors.pdf_path_empty": "PDFパスが空です", "anki.pdf_note.errors.text_data_empty": "テキストデータが空です", "anki.pdf_note.errors.unsupported_link_format": "サポートされていないリンク形式", "anki.pdf_note.formats.custom": "カスタム", "anki.pdf_note.formats.html": "HTML", "anki.pdf_note.formats.markdown": "<PERSON><PERSON>", "anki.pdf_note.formats.url": "Url", "anki.pdf_note.notifications.all_notes_copied_success": "すべてのノートをコピーしました", "anki.pdf_note.notifications.all_notes_copy_failed": "すべてのノートの挿入に失敗しました", "anki.pdf_note.notifications.annotation_copied_success": "注釈をコピーしました", "anki.pdf_note.notifications.annotation_copy_failed": "注釈の挿入に失敗しました", "anki.pdf_note.notifications.bookmark_links_copied_success": "ブックマークリンクをコピーしました", "anki.pdf_note.notifications.bookmark_links_copy_failed": "ブックマークリンクの挿入に失敗しました", "anki.pdf_note.notifications.flag_a_set_failed": "フラグAの設定に失敗しました", "anki.pdf_note.notifications.flag_a_set_success": "フラグAを設定しました", "anki.pdf_note.notifications.flag_b_set_failed": "フラグBの設定に失敗しました", "anki.pdf_note.notifications.flag_b_set_success": "フラグBを設定しました", "anki.pdf_note.notifications.flag_cleared_success": "フラグをクリアしました", "anki.pdf_note.notifications.flag_jump_failed": "フラグへのジャンプに失敗しました", "anki.pdf_note.notifications.go_end_failed": "最後のページへの移動に失敗しました", "anki.pdf_note.notifications.go_home_failed": "最初のページへの移動に失敗しました", "anki.pdf_note.notifications.go_next_failed": "次のページへの移動に失敗しました", "anki.pdf_note.notifications.go_prev_failed": "前のページへの移動に失敗しました", "anki.pdf_note.notifications.link_copied_success": "リンクをコピーしました", "anki.pdf_note.notifications.link_copy_failed": "リンクの挿入に失敗しました", "anki.pdf_note.notifications.note_copied_success": "ノートをコピーしました", "anki.pdf_note.notifications.note_copy_failed": "ノートの挿入に失敗しました", "anki.pdf_note.notifications.ocr_failed": "OCRに失敗しました", "anki.pdf_note.notifications.ocr_in_development": "OCR機能は開発中です", "anki.pdf_note.notifications.page_screenshot_failed": "ページスクリーンショットの挿入に失敗しました", "anki.pdf_note.notifications.page_screenshot_in_development": "ページスクリーンショット機能は開発中です", "anki.pdf_note.notifications.page_text_extract_failed": "ページテキストの抽出に失敗しました", "anki.pdf_note.notifications.page_text_extract_in_development": "ページテキスト抽出機能は開発中です", "anki.pdf_note.notifications.please_set_flag_first": "まずフラグを設定してください", "anki.pdf_note.notifications.scroll_down_failed": "下スクロールに失敗しました", "anki.pdf_note.notifications.scroll_up_failed": "上スクロールに失敗しました", "anki.pdf_note.notifications.settings_saved_success": "設定を保存しました", "anki.pdf_note.notifications.shortcuts_disabled": "すべてのショートカットを無効にしました", "anki.pdf_note.notifications.shortcuts_enabled": "すべてのショートカットを有効にしました", "anki.pdf_note.shortcuts.clear_flag": "フラグをクリア", "anki.pdf_note.shortcuts.disable_all": "すべて無効", "anki.pdf_note.shortcuts.go_end": "最後のページへ", "anki.pdf_note.shortcuts.go_home": "最初のページへ", "anki.pdf_note.shortcuts.go_next": "次のページ", "anki.pdf_note.shortcuts.go_prev": "前のページ", "anki.pdf_note.shortcuts.insert_all_notes": "すべてのノートを挿入", "anki.pdf_note.shortcuts.insert_bookmarks_link": "ブックマークリンクを挿入", "anki.pdf_note.shortcuts.insert_comment": "注釈を挿入", "anki.pdf_note.shortcuts.insert_comment_link": "注釈リンクを挿入", "anki.pdf_note.shortcuts.insert_note": "ノートを挿入", "anki.pdf_note.shortcuts.insert_page_link": "ページリンクを挿入", "anki.pdf_note.shortcuts.insert_path_link": "パスリンクを挿入", "anki.pdf_note.shortcuts.jump_to_flag": "フラグにジャンプ", "anki.pdf_note.shortcuts.not_set": "未設定", "anki.pdf_note.shortcuts.set_flag_a": "フラグAを設定", "anki.pdf_note.shortcuts.set_flag_b": "フラグBを設定", "anki.pdf_note.tabs.config": "設定", "anki.pdf_note.tabs.shortcuts": "ショートカット", "anki.pdf_note.title": "PDFノート", "anki.placeholder.a_item_id": "解答項目IDを入力", "anki.placeholder.a_item_id_required": "解答項目IDは必須です", "anki.placeholder.a_page_range": "解答ファイルページ範囲", "anki.placeholder.atLeastOneAnnotationType": "少なくとも1つの注釈タイプを選択", "anki.placeholder.atLeastOneAnswerClozeGrammar": "少なくとも1つの穴埋め文法を選択", "anki.placeholder.atLeastOneMaskType": "少なくとも1つのマスクタイプを選択", "anki.placeholder.extra_info": "追加情報を入力（例：《書籍名》）", "anki.placeholder.input_item_id": "項目IDを入力", "anki.placeholder.input_tags": "タグを入力", "anki.placeholder.item_id_required": "項目IDは必須です", "anki.placeholder.mustBeGreaterThan0": "0より大きい必要があります", "anki.placeholder.mustBeInteger": "整数を入力してください", "anki.placeholder.page_range": "ページ範囲を入力（空欄は全ページ）", "anki.placeholder.page_range_required": "正しいページ範囲を入力（例: 1-3,5-7）", "anki.placeholder.pdf_columns": "PDF列数を入力", "anki.placeholder.pdf_columns_required": "PDF列数は必須です", "anki.placeholder.q_item_id": "問題項目IDを入力", "anki.placeholder.q_item_id_required": "問題項目IDは必須です", "anki.placeholder.q_page_range": "問題ファイルページ範囲", "anki.placeholder.select_a_file": "解答ファイルを選択", "anki.placeholder.select_file": "圧縮する画像ファイルを選択", "anki.placeholder.select_q_file": "問題ファイルを選択", "anki.placeholder.select_tags": "タグを選択", "anki.placeholder.tags": "タグを選択または入力", "anki.placeholder.target_deck_search_input": "デッキを入力", "anki.placeholder.zotero_item_id": "項目IDを入力", "anki.placeholder.zotero_item_id_required": "項目IDは必須です", "anki.sep": "区切り文字", "anki.sync.config.dataLocation": "データ保存場所", "anki.sync.config.dataLocationPlaceholder": "データ保存場所を選択してください", "anki.sync.config.host": "リッスンアドレス", "anki.sync.config.hostInvalid": "有効なIPアドレスまたはドメイン名を入力してください", "anki.sync.config.hostPlaceholder": "リッスンアドレスを入力してください", "anki.sync.config.hostRequired": "リッスンアドレスは必須です", "anki.sync.config.max_payloa_invalid": "正の整数を入力してください。例: 100", "anki.sync.config.max_payloa_required": "最大同期ペイロードは必須です", "anki.sync.config.max_payload": "最大同期ペイロード(MB)", "anki.sync.config.max_payload_placeholder": "最大同期ペイロードを入力、デフォルトは100", "anki.sync.config.password": "パスワード", "anki.sync.config.passwordMinLength": "パスワードは6文字以上である必要があります", "anki.sync.config.passwordPlaceholder": "パスワードを入力してください", "anki.sync.config.passwordRequired": "パスワードは必須です", "anki.sync.config.port": "リッスンポート", "anki.sync.config.portInvalid": "1-65535の範囲でポート番号を入力してください", "anki.sync.config.portPlaceholder": "リッスンポートを入力してください", "anki.sync.config.portRequired": "リッスンポートは必須です", "anki.sync.config.title": "同期設定", "anki.sync.config.username": "ユーザー名", "anki.sync.config.usernamePlaceholder": "ユーザー名を入力してください", "anki.sync.config.usernameRequired": "ユーザー名は必須です", "anki.sync.configIncomplete": "設定を完全に入力してください", "anki.sync.copied": "コピーしました: @address", "anki.sync.logStartServerError": "サーバー起動エラー", "anki.sync.logStopServerError": "サーバー停止エラー", "anki.sync.needNotificationPermission": "同期サーバーを起動するには通知権限が必要です", "anki.sync.no_user": "ユーザーがいません。まず追加してください", "anki.sync.serverAddress": "サーバーアドレス:", "anki.sync.serverStarted": "同期サーバーが起動しました!", "anki.sync.serverStopped": "同期サーバーが停止しました", "anki.sync.setDataLocation": "データ保存場所を設定してください", "anki.sync.settingsSaved": "設定が保存されました", "anki.sync.startServerError": "サーバー起動エラー: {error}", "anki.sync.startServerFailed": "同期サーバーの起動に失敗しました: {message}", "anki.sync.startingServer": "サーバーを起動中...", "anki.sync.stopServerError": "サーバー停止エラー", "anki.sync.stopServerFailed": "同期サーバーの停止に失敗しました", "anki.sync.syncServer": "同期サーバー", "anki.sync.title": "<PERSON><PERSON>期", "anki.sync.user.add": "追加", "anki.sync.user.addError": "ユーザーの追加に失敗しました", "anki.sync.user.addFirst": "ユーザーを追加", "anki.sync.user.addSuccess": "ユーザーの追加に成功しました", "anki.sync.user.addTitle": "新しいユーザーを追加", "anki.sync.user.cancel": "キャンセル", "anki.sync.user.delete": "削除", "anki.sync.user.deleteConfirm": "ユーザー'@username'を削除してもよろしいですか？", "anki.sync.user.deleteError": "ユーザーの削除に失敗しました", "anki.sync.user.deleteSuccess": "ユーザーの削除に成功しました", "anki.sync.user.deleteTitle": "ユーザーを削除", "anki.sync.user.edit": "編集", "anki.sync.user.editTitle": "ユーザーを編集", "anki.sync.user.noUsers": "設定されたユーザーがありません", "anki.sync.user.notFound": "ユーザーが見つかりません", "anki.sync.user.title": "ユーザー管理", "anki.sync.user.update": "更新", "anki.sync.user.updateError": "ユーザーの更新に失敗しました", "anki.sync.user.updateSuccess": "ユーザーの更新に成功しました", "anki.sync.user.usernameExists": "ユーザー名は既に存在します", "anki.text": "テキスト", "anki.text_cannot_empty": "テキストは空にできません", "anki.text_card.answer_after_option": "解答は選択肢の後", "anki.text_card.answer_cloze": "解答穴埋め", "anki.text_card.answer_file_fields": "解答ファイル含有フィールド", "anki.text_card.answer_in_question": "解答は問題文内", "anki.text_card.at_least_one_cloze_grammar": "少なくとも1つの穴埋め文法を選択してください", "anki.text_card.card_template": "カードテンプレート", "anki.text_card.choice_tab": "選択問題", "anki.text_card.cloze_grammar": "穴埋め文法", "anki.text_card.cloze_tab": "穴埋め問題", "anki.text_card.document_type": "ドキュメントタイプ", "anki.text_card.feature_description": "TXT、Markdownなど多種類形式ファイルのカード作成をサポート、穴埋めカード、問答カード、選択問題、判断問題作成をサポート、複数のカード作成モードをサポート", "anki.text_card.file_path_not_exist": "ファイルパスが存在しません", "anki.text_card.fixed_options_true_false": "正||誤", "anki.text_card.free_guess": "自由推測", "anki.text_card.function_description": "機能説明", "anki.text_card.ignore_group": "グループを無視", "anki.text_card.input_subdeck_prefix": "サブデッキプレフィックスを入力", "anki.text_card.input_tags": "タグを入力", "anki.text_card.judge_tab": "判断問題", "anki.text_card.mask_all_guess_all": "全隠し全推測", "anki.text_card.mask_all_guess_one": "全隠し単一推測", "anki.text_card.mask_one_guess_one": "単一隠し単一推測", "anki.text_card.media_file_directory": "メディアファイルディレクトリ", "anki.text_card.media_folder_placeholder": "メディアフォルダ、空欄の場合はファイルと同じディレクトリを使用", "anki.text_card.none": "なし", "anki.text_card.normal_separator": "通常区切り", "anki.text_card.obsidian_syntax": "Obsidian構文", "anki.text_card.one_cloze_per_card": "1穴1カード", "anki.text_card.please_select_answer_file": "解答ファイルを選択してください", "anki.text_card.please_select_file": "ファイルを選択してください", "anki.text_card.please_select_file_not_directory": "ディレクトリではなく、ファイルを選択してください", "anki.text_card.please_select_files": "ファイルを選択してください！", "anki.text_card.please_select_kevin_choice_template": "【Kevin Choice Card v2】テンプレートを選択してください", "anki.text_card.please_select_kevin_text_cloze_template": "【Kevin Text Cloze v3】テンプレートを選択してください", "anki.text_card.please_select_question_answer_files": "問題ファイルと解答ファイルを選択してください！", "anki.text_card.please_select_question_file": "問題ファイルを選択してください", "anki.text_card.please_set_answers_field_pattern": "Answersフィールドのマッチングパターンを設定してください！", "anki.text_card.please_set_front_field_pattern": "Frontフィールドのマッチングパターンを設定してください！", "anki.text_card.please_set_question_field_pattern": "Questionフィールドのマッチングパターンを設定してください！", "anki.text_card.processing_question": "@current問目を処理中", "anki.text_card.qa_tab": "問答問題", "anki.text_card.question_answer_count_mismatch": "デッキ@deckの問題数（@questionCount）と解答数（@answerCount）が一致しません！", "anki.text_card.question_answer_cross_file": "問題解答異なるファイル", "anki.text_card.question_answer_cross_file_choice": "問題解答異なるファイル", "anki.text_card.question_answer_same_file": "問題解答同一ファイル", "anki.text_card.question_file_fields": "問題ファイル含有フィールド", "anki.text_card.question_single_line": "問題単行", "anki.text_card.regex_separator": "正規表現区切り", "anki.text_card.restore_params": "パラメータを復元", "anki.text_card.save_params": "パラメータを保存", "anki.text_card.select_answer_file_fields": "解答ファイル含有フィールドを選択", "anki.text_card.select_answer_file_fields_required": "解答ファイル含有フィールドを選択してください", "anki.text_card.select_answer_file_placeholder": "解答ファイルを選択してください", "anki.text_card.select_card_template": "カードテンプレートを選択", "anki.text_card.select_question_file_fields": "問題ファイル含有フィールドを選択", "anki.text_card.select_question_file_fields_required": "問題ファイル含有フィールドを選択してください", "anki.text_card.select_subdeck_prefix": "サブデッキプレフィックスを選択", "anki.text_card.select_tags": "タグを選択", "anki.text_card.subdeck_prefix": "サブデッキプレフィックス", "anki.text_card.title": "テキストカード作成", "anki.tts.add_voice": "音声を追加", "anki.tts.card_id": "カードID", "anki.tts.card_id_cannot_empty": "カードIDは空にできません", "anki.tts.card_id_illegal": "カードIDが存在しないか不正です。例：1736773785607,1736773785608", "anki.tts.card_id_placeholder": "カードIDリストを入力、カンマで区切る。例: 1736080247115,1736080247119", "anki.tts.card_template": "カードテンプレート", "anki.tts.card_tts_tab": "カード読み上げ", "anki.tts.card_tts_title": "カード読み上げ", "anki.tts.failed": "失敗", "anki.tts.feature_description": "<PERSON><PERSON><PERSON><PERSON>ドの一括音声追加をサポート、複数の音声エンジンをサポート、テキスト読み上げをサポート", "anki.tts.feature_not_supported_on_current_system": "この機能は現在のシステムではサポートされていません", "anki.tts.fill_field": "フィールドを埋める", "anki.tts.function_description": "機能説明", "anki.tts.input_sep": "区切り文字を入力", "anki.tts.input_text": "テキストを入力...", "anki.tts.is_use_sep": "区切り文字を有効にする", "anki.tts.lang.chinese": "中国語", "anki.tts.lang.english_uk": "英語（イギリス）", "anki.tts.lang.english_us": "英語（アメリカ）", "anki.tts.lang.french": "フランス語", "anki.tts.lang.german": "ドイツ語", "anki.tts.lang.italian": "イタリア語", "anki.tts.lang.japanese": "日本語", "anki.tts.lang.korean": "韓国語", "anki.tts.lang.portuguese": "ポルトガル語", "anki.tts.lang.russian": "ロシア語", "anki.tts.lang.spanish": "スペイン語", "anki.tts.language": "言語", "anki.tts.original_field": "元のフィールド", "anki.tts.output_directory_cannot_be_empty": "出力ディレクトリは空にできません", "anki.tts.pitch": "音調", "anki.tts.please_select_cards_first": "まずカードを選択してください", "anki.tts.processing": "処理中...", "anki.tts.processing_note": "ノートを処理中: @noteId", "anki.tts.rate": "話速", "anki.tts.remove_voice": "音声を削除", "anki.tts.select_card_template": "カードテンプレートを選択", "anki.tts.select_field": "フィールドを選択", "anki.tts.select_language": "言語を選択", "anki.tts.select_sep": "区切り文字を選択", "anki.tts.select_style": "スタイルを選択", "anki.tts.style": "スタイル", "anki.tts.text_cannot_be_empty": "テキストは空にできません", "anki.tts.text_tts_tab": "テキスト読み上げ", "anki.tts.voice.cantonese_hongkong": "広東語-香港", "anki.tts.voice.female": "女性", "anki.tts.voice.male": "男性", "anki.tts.voice.multilingual": "多言語", "anki.tts.voice.northeast_dialect": "東北弁", "anki.tts.voice.shaanxi_dialect": "陝西弁", "anki.tts.voice_field_config": "音声フィールド設定", "anki.tts.voice_generation_failed": "音声生成に失敗しました: @message", "anki.tts.voice_settings": "音声設定", "anki.tts.volume": "音量", "anki.wereader_card.added_highlight_at": "@timeにハイライトを追加", "anki.wereader_card.answer_cloze": "解答穴埋め", "anki.wereader_card.api_error": "エラー: @message", "anki.wereader_card.api_request": "リクエスト: @uri", "anki.wereader_card.api_request_failed": "リクエストに失敗しました: @statusCode", "anki.wereader_card.ascending_order": "昇順", "anki.wereader_card.at_least_one_export_type": "少なくとも1つのエクスポートタイプを選択してください", "anki.wereader_card.at_least_one_note_type": "少なくとも1つのノートタイプを選択してください", "anki.wereader_card.cannot_get_vid": "<PERSON><PERSON><PERSON><PERSON>r_vidを取得できません", "anki.wereader_card.cloze_grammar": "穴埋め文法", "anki.wereader_card.continue_processing_highlights": "ハイライト内容の処理を続行", "anki.wereader_card.cookie_cannot_empty": "<PERSON><PERSON>は空にできません", "anki.wereader_card.cookie_expired_refreshed": "Cookieが期限切れです。<PERSON><PERSON>の更新に成功しました", "anki.wereader_card.cookie_placeholder": "Cookieを入力", "anki.wereader_card.cookie_refresh_error": "Cookie更新に失敗しました: @message", "anki.wereader_card.cookie_refresh_failed": "Cookie更新の試行に失敗しました", "anki.wereader_card.cookie_refreshed_retry": "Cookieを更新しました。ノート抽出を再試行します", "anki.wereader_card.cookie_validation_error": "Cookie検証に失敗しました: @error", "anki.wereader_card.cookie_validation_failed": "Cookie検証に失敗しました", "anki.wereader_card.descending_order": "降順", "anki.wereader_card.enable_separator": "区切り文字を有効にする", "anki.wereader_card.end_time": "終了時間", "anki.wereader_card.export_format": "エクスポート形式", "anki.wereader_card.export_type": "エクスポートタイプ", "anki.wereader_card.extract_hot_failed": "人気ハイライトの抽出に失敗しました: @error", "anki.wereader_card.extract_notes_failed": "ノートの抽出に失敗しました: @error", "anki.wereader_card.feature_description": "微信読書ノートのカード作成をサポート、問答・穴埋めカード作成をサポート、微信読書ノートの一括エクスポートもサポート", "anki.wereader_card.function_description": "機能説明", "anki.wereader_card.generate_excel_failed": "Excelファイルの生成に失敗しました", "anki.wereader_card.get_all_hot_format_error": "全人気ハイライトの取得に失敗しました: 返されたデータ形式が正しくありません", "anki.wereader_card.get_all_hot_success": "全人気ハイライトの取得に成功しました", "anki.wereader_card.get_book_info_failed": "書籍詳細の取得に失敗しました: @message", "anki.wereader_card.get_bookshelf_failed": "本棚の取得に失敗しました: @message", "anki.wereader_card.get_bookshelf_status_failed": "本棚の取得に失敗しました: @statusCode", "anki.wereader_card.get_chapters_failed": "書籍目次の取得に失敗しました: @message", "anki.wereader_card.get_chapters_format_error": "書籍目次の取得に失敗しました: 返されたデータ形式が正しくありません", "anki.wereader_card.get_highlights_failed": "ハイライトの取得に失敗しました: @message", "anki.wereader_card.get_hot_failed": "人気ハイライトの取得に失敗しました: @message", "anki.wereader_card.get_hot_unknown_error": "人気ハイライトの取得中に不明なエラーが発生しました: @error", "anki.wereader_card.get_notebook_failed": "ノートブックの取得に失敗しました: @message, @data, cookie: @cookie", "anki.wereader_card.get_progress_failed": "読書進度情報の取得に失敗しました: @message", "anki.wereader_card.get_reviews_failed": "個人感想の取得に失敗しました: @message", "anki.wereader_card.getting_all_hot": "全@count件の人気ハイライトを取得中...", "anki.wereader_card.getting_hot_count": "人気ハイライト総数を取得中...", "anki.wereader_card.highlight_notes": "ハイライトノート", "anki.wereader_card.highlights_api_format_error": "ハイライトAPIの返されたデータ形式が正しくありません: @result", "anki.wereader_card.highlights_api_format_error_simple": "ハイライトAPIの返されたデータ形式が正しくありません", "anki.wereader_card.hot_api_format_error": "人気ハイライトAPIの返されたデータ形式が正しくありません", "anki.wereader_card.hot_highlights": "人気ハイライト", "anki.wereader_card.hot_highlights_format_error": "人気ハイライトの取得に失敗しました: 返されたデータ形式が正しくありません", "anki.wereader_card.input_book": "書籍を入力", "anki.wereader_card.input_cloze_grammar": "穴埋め文法を入力", "anki.wereader_card.input_separator": "区切り文字を入力", "anki.wereader_card.input_tags": "タグを入力", "anki.wereader_card.limit_time_range": "時間範囲を限定", "anki.wereader_card.my_notes": "マイノート", "anki.wereader_card.no_hot_highlights": "人気ハイライトがありません。初期リクエスト結果を返します", "anki.wereader_card.note_card_tab": "ノートカード作成", "anki.wereader_card.note_export_tab": "ノートエクスポート", "anki.wereader_card.note_source": "ノートソース", "anki.wereader_card.note_type": "ノートタイプ", "anki.wereader_card.output_directory": "出力ディレクトリ", "anki.wereader_card.people_highlighted": "@count人がハイライト", "anki.wereader_card.processing": "処理中...", "anki.wereader_card.published_at": "@time発表", "anki.wereader_card.published_thought_at": "@time感想を発表：", "anki.wereader_card.review_notes": "感想ノート", "anki.wereader_card.reviews_api_format_error": "個人感想APIの返されたデータ形式が正しくありません: @result", "anki.wereader_card.reviews_api_format_error_simple": "個人感想APIの返されたデータ形式が正しくありません", "anki.wereader_card.select_cloze_grammar": "穴埋め文法を選択", "anki.wereader_card.select_end_time": "終了時間を選択", "anki.wereader_card.select_output_directory": "出力ディレクトリを選択してください", "anki.wereader_card.select_separator": "区切り文字を選択", "anki.wereader_card.select_start_time": "開始時間を選択", "anki.wereader_card.select_tags": "タグを選択", "anki.wereader_card.select_target_book": "対象書籍を選択", "anki.wereader_card.separator": "区切り文字", "anki.wereader_card.sort_by_chapter": "章節順", "anki.wereader_card.sort_by_create_time": "追加時間順", "anki.wereader_card.sort_by_popularity": "人気度順", "anki.wereader_card.sort_direction": "ソート方向", "anki.wereader_card.sort_method": "ソート方法", "anki.wereader_card.start_time": "開始時間", "anki.wereader_card.target_book": "対象書籍", "anki.wereader_card.title": "微信読書カード作成", "anki.word_card.answer_after_option": "解答は選択肢の後", "anki.word_card.answer_cloze": "解答穴埋め", "anki.word_card.answer_file_fields": "解答ファイル含有フィールド", "anki.word_card.answer_in_question": "解答は問題文内", "anki.word_card.at_least_one_cloze_grammar": "少なくとも1つの穴埋め文法を選択してください", "anki.word_card.at_least_one_cloze_style": "少なくとも1つの穴埋めスタイルを選択してください", "anki.word_card.blue_color": "青色(#0000FF)", "anki.word_card.bold": "太字", "anki.word_card.card_template": "カードテンプレート", "anki.word_card.choice_tab": "選択問題", "anki.word_card.cloze_grammar": "穴埋め文法", "anki.word_card.cloze_style": "穴埋めスタイル", "anki.word_card.cloze_tab": "穴埋め問題", "anki.word_card.convert_to_html": "", "anki.word_card.extract_card": "", "anki.word_card.feature_description": "docx形式ファイルのカード作成をサポート、穴埋めカード、問答カード、選択問題、判断問題作成をサポート、複数のカード作成モードをサポート", "anki.word_card.file_path_not_exist": "ファイルパスが存在しません", "anki.word_card.fixed_options_true_false": "正||誤", "anki.word_card.free_guess": "自由推測", "anki.word_card.function_description": "機能説明", "anki.word_card.green_color": "緑色(#00FF00)", "anki.word_card.guru_import": "Guruインポート", "anki.word_card.highlight_color": "ハイライト色", "anki.word_card.input_color_placeholder": "色を入力、例: #FF0000", "anki.word_card.input_subdeck_prefix": "サブデッキプレフィックスを入力", "anki.word_card.input_tags": "タグを入力", "anki.word_card.italic": "斜体", "anki.word_card.judge_tab": "判断問題", "anki.word_card.mask_all_guess_all": "全隠し全推測", "anki.word_card.mask_all_guess_one": "全隠し単一推測", "anki.word_card.mask_one_guess_one": "単一隠し単一推測", "anki.word_card.none": "なし", "anki.word_card.normal_separation": "通常区切り", "anki.word_card.one_cloze_per_card": "1穴1カード", "anki.word_card.please_select_answer_file": "解答ファイルを選択してください", "anki.word_card.please_select_file": "ファイルを選択してください", "anki.word_card.please_select_file_not_directory": "ディレクトリではなく、ファイルを選択してください", "anki.word_card.please_select_files": "ファイルを選択してください！", "anki.word_card.please_select_kevin_choice_template": "【Kevin Choice Card v2】テンプレートを選択してください", "anki.word_card.please_select_kevin_text_cloze_template": "【Kevin Text Cloze v3】テンプレートを選択してください", "anki.word_card.please_select_question_answer_files": "問題ファイルと解答ファイルを選択してください！", "anki.word_card.please_select_question_file": "問題ファイルを選択してください", "anki.word_card.please_set_answers_field_pattern": "Answersフィールドのマッチングパターンを設定してください！", "anki.word_card.please_set_front_field_pattern": "Frontフィールドのマッチングパターンを設定してください！", "anki.word_card.please_set_question_field_pattern": "Questionフィールドのマッチングパターンを設定してください！", "anki.word_card.processing_question": "@current問目を処理中", "anki.word_card.qa_tab": "問答問題", "anki.word_card.question_answer_count_mismatch": "デッキ@deckの問題数（@questionCount）と解答数（@answerCount）が一致しません！", "anki.word_card.question_answer_cross_file": "問題解答異なるファイル", "anki.word_card.question_answer_cross_file_choice": "問題解答異なるファイル", "anki.word_card.question_answer_same_file": "問題解答同一ファイル", "anki.word_card.question_file_fields": "問題ファイル含有フィールド", "anki.word_card.question_single_line": "問題単行", "anki.word_card.red_color": "赤色(#FF0000)", "anki.word_card.regex_separation": "正規表現区切り", "anki.word_card.restore_params": "パラメータを復元", "anki.word_card.save_params": "パラメータを保存", "anki.word_card.select_answer_file_fields": "解答ファイル含有フィールドを選択", "anki.word_card.select_answer_file_fields_required": "解答ファイル含有フィールドを選択してください", "anki.word_card.select_answer_file_placeholder": "解答ファイルを選択してください", "anki.word_card.select_card_template": "カードテンプレートを選択", "anki.word_card.select_color": "色を選択", "anki.word_card.select_question_file_fields": "問題ファイル含有フィールドを選択", "anki.word_card.select_question_file_fields_required": "問題ファイル含有フィールドを選択してください", "anki.word_card.select_subdeck_prefix": "サブデッキプレフィックスを選択", "anki.word_card.select_tags": "タグを選択", "anki.word_card.strikethrough": "取り消し線", "anki.word_card.subdeck_prefix": "サブデッキプレフィックス", "anki.word_card.text_color": "テキスト色", "anki.word_card.text_color_option": "テキスト色", "anki.word_card.text_highlight": "テキストハイライト", "anki.word_card.title": "Wordカード作成", "anki.word_card.underline": "下線", "anki.word_card.yellow_color": "黄色(#FFFF00)", "ankiConfig.ankiConnectAddress": "AnkiConnectアドレス", "ankiConfig.ankiPath": "<PERSON><PERSON>", "ankiConfig.autoStartAnki": "Ankiを自動起動", "ankiConfig.cardMode": "カード作成モード", "ankiConfig.ffmpegPath": "FFmpegのパス", "ankiConfig.outputDirectory": "出力ディレクトリ", "ankiConfig.outputDirectoryPlaceholder": ".apkg出力ディレクトリ", "ankiConfig.pdfReaderPath": "PDFリーダーのパス", "ankiConfig.selectAnkiPath": "Ankiのパスを選択してください", "ankiConfig.selectCardMode": "カード作成モードを選択", "ankiConfig.selectFfmpegPath": "FFmpegのパスを選択してください", "ankiConfig.selectPdfReaderPath": "PDFリーダーのパスを選択してください", "ankiConfig.title": "環境設定", "common.colorPicker.cancel": "キャンセル", "common.colorPicker.confirm": "確認", "common.colorPicker.selectColor": "色を選択", "common.completed": "完了", "common.error": "失敗", "common.fileSelect.clearAll": "すべてクリア", "common.fileSelect.customDirectory": "カスタムディレクトリ", "common.fileSelect.directoryCannotBeEmpty": "ディレクトリパスは空にできません", "common.fileSelect.directoryNotExist": "ディレクトリパスが存在しません", "common.fileSelect.fileCannotBeEmpty": "ファイルパスは空にできません", "common.fileSelect.fileNotExist": "ファイルが存在しません", "common.fileSelect.overwriteOriginal": "元のファイルを上書き", "common.fileSelect.pleaseSelectFiles": "{format}形式のファイルを選択してください", "common.fileSelect.sameDirectory": "同じディレクトリ", "common.fileSelect.selectDirectoryNotFile": "ファイルではなく、ディレクトリを選択してください", "common.fileSelect.selectFileNotDirectory": "ディレクトリではなく、ファイルを選択してください", "common.fileSelect.selectedFiles": "{count}個のファイルを選択しました", "common.ui.emptyList": "リストは空です", "common.ui.fieldMapping": "フィールドマッピング", "common.ui.keepPrefix": "プレフィックスを保持", "common.ui.matchMode": "一致モード", "common.ui.searchPlaceholder": "検索内容を入力...", "common.ui.selectOrEnter": "選択または入力", "common.ui.templateField": "テンプレートフィールド", "home.sections.ankiCards": "Ankiカード作成", "home.sections.ankiEnhance": "<PERSON><PERSON>強化", "home.sections.conversion": "変換ツール", "home.sections.notes": "効率的なノート", "home.sections.pdfEdit": "PDF編集", "home.tool.vocab_card": "単語カード作成", "home.tools.aiCard": "AIカード作成", "home.tools.ankiSync": "<PERSON><PERSON>期", "home.tools.cardOcr": "カードOCR", "home.tools.cardTts": "カード読み上げ", "home.tools.card_media_manager": "カードメディア管理", "home.tools.deckManager": "デッキ管理", "home.tools.docxToHtml": "DOCXからHTMLへ", "home.tools.epubToPdf": "EPUBからPDFへ", "home.tools.excelCard": "Excelからカード作成", "home.tools.flashNote": "クイックノート", "home.tools.imageCard": "画像からカード作成", "home.tools.image_ocr": "画像OCR", "home.tools.imgToPdf": "画像からPDFへ", "home.tools.markdownCard": "Markdownからカード作成", "home.tools.mdToHtml": "MDからHTMLへ", "home.tools.mediaCard": "マルチメディアからカード作成", "home.tools.mindmapCard": "マインドマップからカード作成", "home.tools.mobiToPdf": "MOBIからPDFへ", "home.tools.mubuCard": "幕布からカード作成", "home.tools.pdfAnnot": "PDF注釈", "home.tools.pdfBackground": "PDF背景", "home.tools.pdfBookmark": "PDFブックマーク", "home.tools.pdfCard": "PDFからカード作成", "home.tools.pdfCombine": "PDFページ結合", "home.tools.pdfCrop": "PDFトリミング", "home.tools.pdfCut": "PDFカット", "home.tools.pdfDelete": "PDFページ削除", "home.tools.pdfEncrypt": "PDF暗号化", "home.tools.pdfExpand": "PDF拡張", "home.tools.pdfExtract": "PDF抽出", "home.tools.pdfInsert": "PDF挿入", "home.tools.pdfMerge": "PDF結合", "home.tools.pdfMeta": "PDFメタ情報", "home.tools.pdfNote": "PDFノート", "home.tools.pdfPageNumber": "PDFページ番号", "home.tools.pdfReorder": "PDFページ並べ替え", "home.tools.pdfRotate": "PDF回転", "home.tools.pdfScale": "PDF拡大・縮小", "home.tools.pdfSplit": "PDF分割", "home.tools.pdfToDocx": "PDFからDOCXへ", "home.tools.pdfToImg": "PDFから画像へ", "home.tools.pdfToImgPdf": "PDFを画像形式PDFへ", "home.tools.pdfWatermark": "PDF透かし", "home.tools.pdf_ocr": "PDF OCR", "home.tools.recoverPermission": "権限を復元", "home.tools.textCard": "テキストからカード作成", "home.tools.videoNote": "ビデオノート", "home.tools.wechatReaderCard": "微信读书からカード作成", "home.tools.wordCard": "Wordからカード作成", "hotkey.action.cancel": "キャンセル", "hotkey.action.confirm": "確認", "hotkey.dialog.title": "ホットキー設定", "hotkey.error.enterShortcut": "ショートカットを入力してください", "hotkey.error.requireModifier": "修飾キーが必要です", "hotkey.error.title": "エラー", "license.activation.failed": "アクティベーション失敗", "license.activation.success": "アクティベーション成功", "license.faq.lifetime_10years.content": "ライフタイムパッケージのアクティベーションコードは10年周期で発行され、期限切れ時に無料で更新できます。", "license.faq.lifetime_10years.title": "なぜライフタイムパッケージのアクティベーションコードは10年間だけですか？", "license.faq.reinstall.content": "いいえ、システムを再インストールしたり新しいデバイスに変更したりする場合にのみ登録解除が必要です。ソフトウェアを再インストール後にアクティベーション状態を失った場合は、再度アクティベートするだけです。同じデバイスでの重複アクティベーションは追加のアクティベーション回数を消費しません。", "license.faq.reinstall.title": "ソフトウェアをアンインストールして再インストールする際に登録解除が必要ですか？", "license.faq.why_activate.content": "このソフトウェアは機能豊富で強力、美しく快適なインターフェース、マルチプラットフォームオフライン利用可能、プライバシー漏洩の心配なし、クリーンで広告なし、多くのPDF処理機能を直接無料で無制限に使用でき、日常の仕事や学習の良いヘルパーです。しかし、ソフトウェアの健全な発展のため、ユーザーにより良い使用体験を継続的に提供するため、一部の有料機能（Ankiカード作成、ビデオノートなど）を内蔵しています。アクティベーションコードを購入してサポートを示し、ソフトウェアのすべての機能をアンロックできます。注意：このソフトウェアは個人ユーザーの合理的な範囲内での使用に限定されており、このソフトウェアを使用して直接的または間接的な利益を生む商業行為については、作者に連絡して別途商用ライセンスを購入してください。", "license.faq.why_activate.title": "なぜアクティベーションが必要ですか？", "license.request.failed": "失敗", "license.trial.failed": "トライアル認証の取得に失敗", "license.trial.success": "トライアルアクティベーション成功", "license.unregister.failed": "登録解除失敗", "license.unregister.success": "登録解除成功", "navigation.home": "ホーム", "navigation.settings": "設定", "paywall.benefitsTitle": "会員限定特典", "paywall.features.aiCard": "高度なAIカード作成", "paywall.features.eudicCard": "Eudicからカード作成", "paywall.features.excelCard": "Excelからカード作成", "paywall.features.fastSync": "高速同期サーバー", "paywall.features.imageCard": "画像穴埋めカード作成", "paywall.features.logseqCard": "Logseqからカード作成", "paywall.features.markdownCard": "Markdownからカード作成", "paywall.features.mubuCard": "幕布からカード作成", "paywall.features.multimediaCard": "マルチメディアカード作成", "paywall.features.multipleChoiceCard": "選択問題の一括カード作成", "paywall.features.notionCard": "Notionからカード作成", "paywall.features.obsidianCard": "Obsidianからカード作成", "paywall.features.pdfCard": "PDFからカード作成", "paywall.features.prioritySupport": "優先技術サポート", "paywall.features.quickNote": "クイックノート機能", "paywall.features.siyuanCard": "思源笔记からカード作成", "paywall.features.textCard": "テキストからカード作成", "paywall.features.textToSpeech": "テキスト読み上げ機能", "paywall.features.videoNote": "ビデオノート機能", "paywall.features.wechatReadCard": "微信读书からカード作成", "paywall.features.wordCard": "Wordからカード作成", "paywall.features.wordListCard": "単語リストからカード作成", "paywall.features.xmindCard": "Xmindからカード作成", "paywall.features.zhixiCard": "知犀导图からカード作成", "paywall.headerSubtitle": "すべての高度な機能のロックを解除し、学習を効率化", "paywall.headerTitle": "PDF Guru Anki プレミアム", "paywall.lifetimeSubscription": "永久ライセンス", "paywall.monthlySubscription": "月間サブスクリプション", "paywall.mostPopular": "一番人気", "paywall.oneTimePurchase": "一回払い", "paywall.perMonth": "/月", "paywall.perYear": "/年", "paywall.privacyPolicy": "プライバシーポリシー", "paywall.purchase": "今すぐ購入", "paywall.restorePurchase": "購入を復元", "paywall.selectPlan": "プランを選択してください", "paywall.subscriptionDetails": "「購入」をクリックすると、「利用規約」と「プライバシーポリシー」を読み、同意したことになります。支払いはApple IDアカウントから引き落とされます。サブスクリプションは、更新日の少なくとも1日前にApple Storeのアカウント設定でいつでもキャンセルできます。", "paywall.subscriptionNotice": "サブスクリプションに関するお知らせ", "paywall.termsOfService": "利用規約", "paywall.title": "メンバーシップ購入", "paywall.yearlySubscription": "年間サブスクリプション", "progress.button.open": "開く", "progress.button.play": "再生", "progress.button.share": "共有", "progress.dialog.title": "進行状況", "progress.error.cannotGetButtonPosition": "ボタンの位置を取得できません", "progress.error.cannotGetContext": "コンテキストを取得できません", "progress.error.cannotOpenFile": "ファイルを開けません", "progress.error.cannotOpenFolder": "フォルダを開けません", "progress.error.cannotShareFile": "ファイルを共有できません", "progress.error.filePathNotExist": "ファイルパスが存在しません", "progress.error.shareDirectoryNotSupported": "ディレクトリの共有はサポートされていません", "progress.error.title": "エラー", "progress.status.completed": "完了", "progress.status.fileSavedTo": "ファイルが保存されました: ", "progress.status.initializing": "初期化中", "progress.status.processing": "処理中", "progress.status.totalProgress": "全体の進行状況", "progress.time.elapsedTime": "経過時間", "progress.time.startTime": "開始時間", "service.browser.cannotConnect": "ブラウザ拡張機能に接続できません", "service.browser.cannotOpenVideo": "ブラウザで動画を開けません。接続状態を確認してください", "service.browser.commandSent": "コマンドがブラウザ拡張機能に送信されました", "service.browser.extensionIdentified": "ブラウザ拡張機能が正常に識別されました", "service.browser.extensionInstallPrompt": "ブラウザ拡張機能がインストールされ、アプリに接続されていることを確認してください", "service.browser.extensionRequired": "ブラウザ拡張機能の接続が必要です", "service.browser.navigationFailed": "ブラウザナビゲーションに失敗しました", "service.browser.openingVideo": "ブラウザで動画を開いています", "service.browser.videoWillOpen": "動画がブラウザで開き、指定時間にジャンプします", "service.screenshot.browserFailed": "ブラウザスクリーンショットに失敗しました", "service.screenshot.dataEmpty": "スクリーンショットデータが空です", "service.screenshot.failed": "スクリーンショットに失敗しました", "service.screenshot.saved": "スクリーンショットがクリップボードに保存されました", "service.timestamp.browserExtensionFailed": "ブラウザ拡張機能のタイムスタンプ生成に失敗しました", "service.timestamp.generationFailed": "タイムスタンプ生成に失敗しました", "service.timestamp.linkCopied": "タイムスタンプリンクがコピーされました", "service.timestamp.linkCopiedToClipboard": "タイムスタンプリンクがクリップボードにコピーされました", "service.timestamp.linkSaved": "リンクがクリップボードに保存されました", "service.video.cannotRetrieveInfo": "動画情報を取得できません", "service.video.infoRetrievalFailed": "動画情報の取得に失敗しました", "service.video.jumpedToTime": "指定時間にジャンプしました", "service.video.jumpedToTimestamp": "ブラウザ動画が指定タイムスタンプにジャンプしました", "service.video.openedAndJumped": "動画がブラウザで正常に開かれ、指定時間にジャンプしました", "service.video.openedInBrowser": "動画がブラウザで開かれました", "settings.aboutAndHelp": "アプリについて＆ヘルプ", "settings.activate": "アクティベート", "settings.cardMode.directAnki": "An<PERSON>直接接続", "settings.cardMode.directAnkidroid": "Ankidroid直接接続", "settings.cardMode.exportApkg": "Apkgエクスポート", "settings.displayLanguage": "表示言語", "settings.language": "", "settings.launchAtStartup": "起動時に自動起動", "settings.preferences": "環境設定", "settings.saved": "設定が保存されました", "settings.theme.dark": "ダーク", "settings.theme.light": "ライト", "settings.theme.system": "システムに従う", "settings.themeSettings": "テーマ設定", "settings.userCenter": "ユーザーセンター", "toolbox.annotation.annotationDelete": "注釈の削除", "toolbox.annotation.annotationExport": "注釈のエクスポート", "toolbox.annotation.annotationFile": "注釈ファイル", "toolbox.annotation.annotationFlatten": "注釈のフラット化", "toolbox.annotation.annotationImport": "注釈のインポート", "toolbox.annotation.deleteTab": "注釈を削除", "toolbox.annotation.description": "PDFファイル内の注釈をインポート、エクスポート、または削除します", "toolbox.annotation.exportFormat": "エクスポート形式", "toolbox.annotation.exportTab": "注釈をエクスポート", "toolbox.annotation.flattenTab": "注釈をフラット化", "toolbox.annotation.importFormat": "インポート形式", "toolbox.annotation.importTab": "注釈をインポート", "toolbox.annotation.title": "PDF注釈", "toolbox.background.backgroundColor": "背景色", "toolbox.background.backgroundImage": "背景画像", "toolbox.background.backgroundType": "背景タイプ", "toolbox.background.colorBackground": "単色背景", "toolbox.background.completed": "完了", "toolbox.background.description": "PDFファイルに単色または画像の背景を追加します", "toolbox.background.fileNotExist": "ファイルが存在しません", "toolbox.background.generateBackgroundFailed": "背景PDFの生成に失敗しました", "toolbox.background.imageBackground": "画像背景", "toolbox.background.imageFileNotExist": "画像ファイルが存在しません", "toolbox.background.imageNotSelected": "画像が選択されていません", "toolbox.background.opacity": "不透明度", "toolbox.background.opacityPlaceholder": "不透明度を入力 (0-1)", "toolbox.background.processingFile": "ファイルを処理中", "toolbox.background.scale": "拡大・縮小率", "toolbox.background.scalePlaceholder": "拡大・縮小率を入力", "toolbox.background.title": "PDF背景", "toolbox.background.unknownBackgroundType": "不明な背景タイプ", "toolbox.background.xOffset": "Xオフセット", "toolbox.background.xOffsetPlaceholder": "水平オフセットを入力", "toolbox.background.yOffset": "Yオフセット", "toolbox.background.yOffsetPlaceholder": "垂直オフセットを入力", "toolbox.bookmark.bookmarkFile": "ブックマークファイル", "toolbox.bookmark.bookmarkFileEmpty": "ブックマークファイルは空にできません", "toolbox.bookmark.deleteBookmarks": "ブックマークを削除", "toolbox.bookmark.deleteTab": "ブックマークを削除", "toolbox.bookmark.description": "PDFファイル内のブックマークをインポート、エクスポート、または削除します", "toolbox.bookmark.exportBookmarks": "ブックマークをエクスポート", "toolbox.bookmark.exportFormat": "エクスポート形式", "toolbox.bookmark.exportTab": "ブックマークをエクスポート", "toolbox.bookmark.importBookmarks": "ブックマークをインポート", "toolbox.bookmark.importTab": "ブックマークをインポート", "toolbox.bookmark.title": "PDFブックマーク", "toolbox.combine.col_lr": "列優先（左から右へ）", "toolbox.combine.col_rl": "列優先（右から左へ）", "toolbox.combine.completed": "完了", "toolbox.combine.description": "元のPDFファイルの複数ページを1ページに結合します", "toolbox.combine.landscape": "横", "toolbox.combine.layout_order": "レイアウト順序", "toolbox.combine.numCols": "列数", "toolbox.combine.numColsPlaceholder": "列数を入力", "toolbox.combine.numRows": "行数", "toolbox.combine.numRowsPlaceholder": "行数を入力", "toolbox.combine.portrait": "縦", "toolbox.combine.row_lr": "行優先（左から右へ）", "toolbox.combine.row_rl": "行優先（右から左へ）", "toolbox.combine.title": "PDFページ結合", "toolbox.common.enterPageRange": "正しいページ範囲を入力してください。例: 1-3,5-7", "toolbox.common.error_with_msg": "処理失敗: {error}", "toolbox.common.failure": "失敗", "toolbox.common.fileProcessSuccess": "ファイルの処理に成功しました", "toolbox.common.fileSelect.error": "PDFファイルを選択してください", "toolbox.common.functionDescription": "機能説明", "toolbox.common.inputFile": "入力ファイル", "toolbox.common.inputFilePlaceholder": "ファイルの絶対パスを入力、またはここにファイルをドラッグ＆ドロップ", "toolbox.common.operationFailed": "操作に失敗しました", "toolbox.common.output.error": "出力ディレクトリを選択してください", "toolbox.common.outputDir": "出力ディレクトリ", "toolbox.common.outputDirectory": "出力ディレクトリ", "toolbox.common.outputLocation": "出力先", "toolbox.common.pageRange": "ページ範囲", "toolbox.common.pageRangePlaceholder": "ページ範囲を入力、空の場合はすべて", "toolbox.common.process.completed": "完了", "toolbox.common.process.failed": "操作に失敗しました", "toolbox.common.process.processFailed": "ファイルの処理に失敗しました", "toolbox.common.process.running": "ファイルを処理中", "toolbox.common.process.success": "ファイルの処理に成功しました", "toolbox.common.processing": "処理中...", "toolbox.common.selectOutputLocation": "出力先を選択", "toolbox.common.selectPdfFiles": "PDFファイルを1つ選択してください", "toolbox.common.submit": "実行", "toolbox.convert.common.inputFile": "入力ファイル", "toolbox.convert.common.inputFilePlaceholder": "ファイルの絶対パスを入力、またはここにファイルをドラッグ＆ドロップ", "toolbox.convert.common.outputDirectory": "出力ディレクトリ", "toolbox.convert.common.outputLocation": "出力先", "toolbox.convert.common.selectOutputLocation": "出力先を選択", "toolbox.convert.common.submit": "実行", "toolbox.convert.docx2html.description": "DOCXをHTMLに変換します", "toolbox.convert.docx2html.title": "DOCXからHTMLへ", "toolbox.convert.docx2pdf.description": "DOCXをPDFに変換します", "toolbox.convert.docx2pdf.title": "DOCXからPDFへ", "toolbox.convert.epub2pdf.description": "EPUBをPDFに変換します", "toolbox.convert.epub2pdf.title": "EPUBからPDFへ", "toolbox.convert.html2pdf.customFont": "カスタムフォント", "toolbox.convert.html2pdf.description": "HTMLファイルをPDFドキュメントに変換します", "toolbox.convert.html2pdf.font": "フォント", "toolbox.convert.html2pdf.fontFile": "フォントファイル", "toolbox.convert.html2pdf.fontFilePlaceholder": "フォントファイルの絶対パスを入力、またはここにファイルをドラッグ＆ドロップ", "toolbox.convert.html2pdf.fontSize": "フォントサイズ", "toolbox.convert.html2pdf.fontSizeError1": "フォントサイズを入力してください。例: 12", "toolbox.convert.html2pdf.fontSizeError2": "浮動小数点数を入力してください", "toolbox.convert.html2pdf.fontSizePlaceholder": "フォントサイズを入力", "toolbox.convert.html2pdf.selectFont": "フォントを選択", "toolbox.convert.html2pdf.title": "HTMLからPDFへ", "toolbox.convert.img2pdf.description": "画像をPDFに変換します", "toolbox.convert.img2pdf.mergeFiles": "ファイルを結合", "toolbox.convert.img2pdf.orientation": "用紙の向き", "toolbox.convert.img2pdf.paperSize": "用紙サイズ", "toolbox.convert.img2pdf.selectOrientation": "向きを選択してください", "toolbox.convert.img2pdf.selectPaperSize": "用紙サイズを選択してください", "toolbox.convert.img2pdf.sortBy": "ソート基準", "toolbox.convert.img2pdf.sortDirection": "ソート方向", "toolbox.convert.img2pdf.sortDirections.ascending": "昇順", "toolbox.convert.img2pdf.sortDirections.descending": "降順", "toolbox.convert.img2pdf.sortOptions.byCreateDate": "作成日時", "toolbox.convert.img2pdf.sortOptions.byModDate": "変更日時", "toolbox.convert.img2pdf.sortOptions.byName": "ファイル名順", "toolbox.convert.img2pdf.sortOptions.byNumberPrefix": "ファイル名の先頭番号", "toolbox.convert.img2pdf.sortOptions.byNumberSuffix": "ファイル名の末尾番号", "toolbox.convert.img2pdf.sortOptions.bySelection": "追加順", "toolbox.convert.img2pdf.title": "画像からPDFへ", "toolbox.convert.md2html.description": "MarkdownをHTMLに変換します", "toolbox.convert.md2html.title": "MDからHTMLへ", "toolbox.convert.md2pdf.description": "MarkdownファイルをPDFドキュメントに変換します", "toolbox.convert.md2pdf.title": "MarkdownからPDFへ", "toolbox.convert.mobi2pdf.description": "MOBIをPDFに変換します", "toolbox.convert.mobi2pdf.title": "MOBIからPDFへ", "toolbox.convert.ofd2pdf.description": "OFDをPDFに変換します", "toolbox.convert.ofd2pdf.title": "OFDからPDFへ", "toolbox.convert.pdf2docx.description": "PDFをDOCXに変換します", "toolbox.convert.pdf2docx.title": "PDFからDOCXへ", "toolbox.convert.pdf2img.advancedScaling": "高度なスケーリングオプション", "toolbox.convert.pdf2img.autoScaleToA4": "大きすぎるページを自動的にA4サイズにスケール", "toolbox.convert.pdf2img.description": "PDFの指定ページを画像に変換します", "toolbox.convert.pdf2img.grayscale": "グレースケールに変換", "toolbox.convert.pdf2img.maxHeightPixels": "最大高さ（ピクセル）", "toolbox.convert.pdf2img.maxHeightPixelsPlaceholder": "最大高さのピクセル値、デフォルトは4000", "toolbox.convert.pdf2img.maxPixelsError": "0より大きい有効なピクセル値を入力してください", "toolbox.convert.pdf2img.maxWidthPixels": "最大幅（ピクセル）", "toolbox.convert.pdf2img.maxWidthPixelsPlaceholder": "最大幅のピクセル値、デフォルトは3000", "toolbox.convert.pdf2img.pageRange": "ページ範囲", "toolbox.convert.pdf2img.pageRangeError": "正しいページ範囲を入力してください。例: 1-3,5-7", "toolbox.convert.pdf2img.pageRangePlaceholder": "ページ範囲を入力、空の場合はすべて", "toolbox.convert.pdf2img.resolution": "解像度(DPI)", "toolbox.convert.pdf2img.resolutionError": "正しい解像度を入力してください。例: 300", "toolbox.convert.pdf2img.resolutionPlaceholder": "解像度を入力、デフォルトは300", "toolbox.convert.pdf2img.title": "PDFから画像へ", "toolbox.convert.pdf2img_pdf.description": "PDFを画像形式のPDFに変換します", "toolbox.convert.pdf2img_pdf.title": "PDFを画像形式PDFへ", "toolbox.crop.cropType": "トリミングタイプ", "toolbox.crop.description": "PDFファイル内のページをトリミングします", "toolbox.crop.expandMode": "拡張モード", "toolbox.crop.keepPaperSize": "ページサイズを維持", "toolbox.crop.margin.bottom": "下の余白", "toolbox.crop.margin.bottomPlaceholder": "下の余白を入力", "toolbox.crop.margin.left": "左の余白", "toolbox.crop.margin.leftPlaceholder": "左の余白を入力", "toolbox.crop.margin.right": "右の余白", "toolbox.crop.margin.rightPlaceholder": "右の余白を入力", "toolbox.crop.margin.top": "上の余白", "toolbox.crop.margin.topPlaceholder": "上の余白を入力", "toolbox.crop.outputFormat": "出力形式", "toolbox.crop.stem_append": "_切り取り", "toolbox.crop.title": "PDFトリミング", "toolbox.crop.unit": "単位", "toolbox.cropTypeOptions.annotate": "矩形注釈切り取り", "toolbox.cropTypeOptions.margin": "余白切り取り", "toolbox.cut.custom.hBreakpoints": "水平分割点", "toolbox.cut.custom.hBreakpointsInvalid": "0〜1の小数をカンマで区切って入力してください。例: 0.4,0.7", "toolbox.cut.custom.hBreakpointsPlaceholder": "水平分割点を入力。例：0.4,0.7", "toolbox.cut.custom.vBreakpoints": "垂直分割点", "toolbox.cut.custom.vBreakpointsInvalid": "0〜1の小数をカンマで区切って入力してください。例: 0.4,0.7", "toolbox.cut.custom.vBreakpointsPlaceholder": "垂直分割点を入力。例：0.4,0.7", "toolbox.cut.cutOptions.custom": "カスタムセグメンテーション", "toolbox.cut.cutOptions.grid": "グリッドセグメンテーション", "toolbox.cut.cutOptions.page": "ページセグメンテーション", "toolbox.cut.cutType": "分割タイプ", "toolbox.cut.description": "元のPDFファイル（の指定ページ）を複数ページに分割します", "toolbox.cut.grid.numCols": "列数", "toolbox.cut.grid.numColsPlaceholder": "列数を入力", "toolbox.cut.grid.numColsRequired": "列数を入力してください。例: 1", "toolbox.cut.grid.numRows": "行数", "toolbox.cut.grid.numRowsPlaceholder": "行数を入力", "toolbox.cut.grid.numRowsRequired": "行数を入力してください。例: 1", "toolbox.cut.page.margin.bottom": "下の余白", "toolbox.cut.page.margin.bottomPlaceholder": "下の余白を入力", "toolbox.cut.page.margin.invalid": "0以上の数値を入力してください", "toolbox.cut.page.margin.left": "左の余白", "toolbox.cut.page.margin.leftPlaceholder": "左の余白を入力", "toolbox.cut.page.margin.right": "右の余白", "toolbox.cut.page.margin.rightPlaceholder": "右の余白を入力", "toolbox.cut.page.margin.top": "上の余白", "toolbox.cut.page.margin.topPlaceholder": "上の余白を入力", "toolbox.cut.page.orientation": "向き", "toolbox.cut.page.pageSize": "用紙サイズ", "toolbox.cut.page.selectOrientation": "向きを選択", "toolbox.cut.page.selectPageSize": "用紙サイズを選択", "toolbox.cut.title": "PDFカット", "toolbox.decrypt.password": "パスワード", "toolbox.decrypt.passwordPlaceholder": "パスワードを入力してください", "toolbox.delete.blankTab": "空白ページ", "toolbox.delete.description": "PDFの指定ページを削除します", "toolbox.delete.stem_append": "ページ削除", "toolbox.delete.title": "PDFページ削除", "toolbox.encrypt.decryptTab": "パスワードを削除", "toolbox.encrypt.description": "PDFファイルを暗号化または復号化します", "toolbox.encrypt.encryptTab": "パスワードを設定", "toolbox.encrypt.fileNameAppend.decrypt": "_復号化", "toolbox.encrypt.fileNameAppend.encrypt": "_暗号化", "toolbox.encrypt.mode.decrypt": "復号化", "toolbox.encrypt.mode.encrypt": "暗号化", "toolbox.encrypt.passwordError.mismatch": "入力されたユーザーパスワードが一致しません", "toolbox.encrypt.passwordError.permissionMismatch": "入力された権限パスワードが一致しません", "toolbox.encrypt.passwordError.permissionRequired": "権限パスワードを入力してください", "toolbox.encrypt.passwordError.required": "ユーザーパスワードを入力してください", "toolbox.encrypt.passwordError.validation": "パスワードの検証に失敗しました", "toolbox.encrypt.passwordType": "パスワードタイプ", "toolbox.encrypt.passwordTypeRequired": "少なくとも1つのパスワードタイプを選択してください", "toolbox.encrypt.permission.assembleDocument": "組み立て", "toolbox.encrypt.permission.copyContent": "コピー", "toolbox.encrypt.permission.editAnnotations": "注釈の編集", "toolbox.encrypt.permission.editContent": "内容の編集", "toolbox.encrypt.permission.fillFields": "フォームの入力", "toolbox.encrypt.permission.print": "印刷", "toolbox.encrypt.title": "PDF暗号化", "toolbox.encrypt.type.permission": "権限パスワード", "toolbox.encrypt.type.user": "ユーザーパスワード", "toolbox.encrypt.user.confirmEmpty": "確認パスワードは空にできません", "toolbox.encrypt.user.confirmPassword": "ユーザーパスワードの確認", "toolbox.encrypt.user.confirmPasswordPlaceholder": "もう一度ユーザーパスワードを入力してください", "toolbox.encrypt.user.password": "ユーザーパスワード", "toolbox.encrypt.user.passwordEmpty": "パスワードは空にできません", "toolbox.encrypt.user.passwordMismatch": "パスワードが一致しません", "toolbox.encrypt.user.passwordPlaceholder": "ユーザーパスワードを入力してください", "toolbox.encrypt.user.passwordTooShort": "パスワードは6文字以上である必要があります", "toolbox.expand.blank.margin.bottom": "下の余白", "toolbox.expand.blank.margin.bottomPlaceholder": "下の余白を入力", "toolbox.expand.blank.margin.invalid": "0以上の数値を入力してください", "toolbox.expand.blank.margin.left": "左の余白", "toolbox.expand.blank.margin.leftPlaceholder": "左の余白を入力", "toolbox.expand.blank.margin.right": "右の余白", "toolbox.expand.blank.margin.rightPlaceholder": "右の余白を入力", "toolbox.expand.blank.margin.top": "上の余白", "toolbox.expand.blank.margin.topPlaceholder": "上の余白を入力", "toolbox.expand.blank.unit": "単位", "toolbox.expand.description": "元のPDFファイル（の指定ページ）を与えられたパラメータで拡張します", "toolbox.expand.direction.bottom": "下", "toolbox.expand.direction.left": "左", "toolbox.expand.direction.right": "右", "toolbox.expand.direction.top": "上", "toolbox.expand.expandType": "拡張タイプ", "toolbox.expand.file.bgFile": "背景ファイル", "toolbox.expand.file.bgFilePlaceholder": "背景ファイルの絶対パスを入力、またはここにファイルをドラッグ＆ドロップ", "toolbox.expand.file.direction": "方向", "toolbox.expand.fileNameAppend": "_拡張", "toolbox.expand.mode.blank": "空白で拡張", "toolbox.expand.mode.file": "ファイルで拡張", "toolbox.expand.title": "PDF拡張", "toolbox.expand.unit.pt": "ピクセル", "toolbox.expand.unit.ratio": "比率", "toolbox.extract.description": "PDFのページ、テキスト、画像などを抽出します", "toolbox.extract.extractMode": "抽出モード", "toolbox.extract.fileNameAppend": "_ページ抽出", "toolbox.extract.imageAppend": "_画像抽出", "toolbox.extract.mode.image": "画像", "toolbox.extract.mode.page": "ページ", "toolbox.extract.mode.text": "テキスト", "toolbox.extract.textAppend": "_テキスト抽出", "toolbox.extract.title": "PDF抽出", "toolbox.extract.unsupported": "サポート対象外", "toolbox.extract.unsupportedMessage": "画像の抽出機能は現在利用できません", "toolbox.insert.description": "元のPDFファイルに空白ページまたは指定ファイルを挿入します", "toolbox.insert.fileNameAppend": "_挿入", "toolbox.insert.insertCount": "挿入数", "toolbox.insert.insertFile": "挿入ファイル", "toolbox.insert.insertPage": "挿入ページ番号", "toolbox.insert.insertPosition": "挿入位置", "toolbox.insert.insertType": "挿入タイプ", "toolbox.insert.mode.blank": "空白ページを挿入", "toolbox.insert.mode.file": "ファイルを挿入", "toolbox.insert.orientation": "紙の方向", "toolbox.insert.orientationOptions.landscape": "横", "toolbox.insert.orientationOptions.portrait": "縦", "toolbox.insert.paperSize": "用紙サイズ", "toolbox.insert.position.after_all": "すべてのページの後", "toolbox.insert.position.after_first": "最初のページの後", "toolbox.insert.position.after_last": "最後のページの後", "toolbox.insert.position.after_page": "指定ページの後", "toolbox.insert.position.before_all": "すべてのページの前", "toolbox.insert.position.before_even": "偶数ページの前", "toolbox.insert.position.before_first": "最初のページの前", "toolbox.insert.position.before_last": "最後のページの前", "toolbox.insert.position.before_odd": "奇数ページの前", "toolbox.insert.position.before_page": "指定ページの前", "toolbox.insert.title": "PDF挿入", "toolbox.merge.atLeastTwoFilesRequired": "少なくとも2つのファイルを選択してください", "toolbox.merge.description": "複数のPDFファイルを1つに結合、または1つのPDFの複数ページを1ページに結合します", "toolbox.merge.direction": "ソート方向", "toolbox.merge.directionOptions.ascending": "昇順", "toolbox.merge.directionOptions.descending": "降順", "toolbox.merge.fileNameAppend.multiFile": "_結合", "toolbox.merge.mergeType": "結合タイプ", "toolbox.merge.mode.file": "複数ファイル結合", "toolbox.merge.mode.page": "複数ページ結合", "toolbox.merge.page_merge_stem_append": "_ページ結合", "toolbox.merge.sortBy": "ソート基準", "toolbox.merge.sortByOptions.createDate": "作成日時", "toolbox.merge.sortByOptions.modDate": "変更日時", "toolbox.merge.sortByOptions.name": "ファイル名順", "toolbox.merge.sortByOptions.numberPrefix": "ファイル名の先頭番号", "toolbox.merge.sortByOptions.numberSuffix": "ファイル名の末尾番号", "toolbox.merge.sortByOptions.selection": "追加順", "toolbox.merge.title": "PDF結合", "toolbox.meta.author": "作成者", "toolbox.meta.authorPlaceholder": "作成者を入力", "toolbox.meta.creationDate": "作成日", "toolbox.meta.creationDatePlaceholder": "作成日を入力。例：2021-01-01 00:00:00", "toolbox.meta.creator": "クリエイター", "toolbox.meta.creatorPlaceholder": "クリエイターを入力", "toolbox.meta.description": "PDFファイルのメタ情報を設定します", "toolbox.meta.keywords": "キーワード", "toolbox.meta.keywordsPlaceholder": "キーワードを入力", "toolbox.meta.modDate": "変更日", "toolbox.meta.modDatePlaceholder": "変更日を入力。例：2021-01-01 00:00:00", "toolbox.meta.producer": "プロデューサー", "toolbox.meta.producerPlaceholder": "プロデューサーを入力", "toolbox.meta.subject": "サブジェクト", "toolbox.meta.subjectPlaceholder": "サブジェクトを入力", "toolbox.meta.title": "PDFメタ情報", "toolbox.meta.titlePlaceholder": "タイトルを入力", "toolbox.meta.title_field": "タイトル", "toolbox.ocr.description": "画像からテキストを抽出します", "toolbox.ocr.title": "OCR", "toolbox.pageNumber.alignment": "配置", "toolbox.pageNumber.customFont": "カスタムフォント", "toolbox.pageNumber.customStyle": "カスタムスタイル", "toolbox.pageNumber.customStylePlaceholder": "カスタムスタイルを入力。%pは現在のページ、%Pは総ページ数。例: %p/%P", "toolbox.pageNumber.description": "PDFファイルにページ番号を追加します。", "toolbox.pageNumber.enterFloatNumber": "浮動小数点数を入力してください", "toolbox.pageNumber.fontColor": "フォントカラー", "toolbox.pageNumber.fontFamily": "フォント", "toolbox.pageNumber.fontFile": "フォントファイル", "toolbox.pageNumber.fontFilePlaceholder": "フォントファイルの絶対パスを入力、またはここにファイルをドラッグ＆ドロップ", "toolbox.pageNumber.fontSize": "フォントサイズ", "toolbox.pageNumber.fontSizePlaceholder": "フォントサイズを入力", "toolbox.pageNumber.fontSizeRequired": "フォントサイズを入力してください。例: 12", "toolbox.pageNumber.margin.bottom": "下の余白", "toolbox.pageNumber.margin.bottomPlaceholder": "下の余白を入力", "toolbox.pageNumber.margin.bottomRequired": "下の余白を入力してください", "toolbox.pageNumber.margin.invalid": "0以上の数値を入力してください", "toolbox.pageNumber.margin.left": "左の余白", "toolbox.pageNumber.margin.leftPlaceholder": "左の余白を入力", "toolbox.pageNumber.margin.leftRequired": "左の余白を入力してください", "toolbox.pageNumber.margin.right": "右の余白", "toolbox.pageNumber.margin.rightPlaceholder": "右の余白を入力", "toolbox.pageNumber.margin.rightRequired": "右の余白を入力してください", "toolbox.pageNumber.margin.top": "上の余白", "toolbox.pageNumber.margin.topPlaceholder": "上の余白を入力", "toolbox.pageNumber.margin.topRequired": "上の余白を入力してください", "toolbox.pageNumber.numberPosition": "ページ番号の位置", "toolbox.pageNumber.opacity": "不透明度", "toolbox.pageNumber.opacityPlaceholder": "不透明度を入力 (0-1)", "toolbox.pageNumber.opacityRequired": "不透明度を入力してください。例: 0.5", "toolbox.pageNumber.selectAlignment": "配置を選択", "toolbox.pageNumber.selectFontFamily": "フォントを選択", "toolbox.pageNumber.selectNumberPosition": "ページ番号の位置を選択", "toolbox.pageNumber.selectStyle": "スタイルを選択", "toolbox.pageNumber.style": "ページ番号のスタイル", "toolbox.pageNumber.title": "PDFページ番号", "toolbox.pdf_ocr.is_merge_mode": "マージモード", "toolbox.pdf_ocr.is_show_page_sep": "ページ区切りを表示", "toolbox.permission.description": "保護されたPDFファイルの権限を復元します", "toolbox.permission.title": "権限を復元", "toolbox.reorder.description": "PDFファイル内のページを並べ替えます。", "toolbox.reorder.newPageOrder": "新しいページ順", "toolbox.reorder.newPageOrderPlaceholder": "新しいページ順を入力。例：最初の2ページを入れ替える場合は 2,1,3-N", "toolbox.reorder.reorder": "ページ再編成", "toolbox.reorder.title": "PDFページ並べ替え", "toolbox.replace.blankTab": "空白ページに置換", "toolbox.replace.description": "PDFファイル内のページを置換します。", "toolbox.replace.fileTab": "指定ファイルに置換", "toolbox.replace.title": "PDFページ置換", "toolbox.rotate.description": "PDFファイル内のページを回転させます。", "toolbox.rotate.rotationAngle": "回転角度", "toolbox.rotate.title": "PDF回転", "toolbox.scale.description": "PDFページのサイズを調整します。", "toolbox.scale.enterPositiveNumber": "0以上の数値を入力してください", "toolbox.scale.height": "高さ", "toolbox.scale.heightPlaceholder": "ページの高さを入力 (pt)", "toolbox.scale.heightRequired": "高さを入力してください", "toolbox.scale.paperSize": "用紙サイズ", "toolbox.scale.scaleRatio": "拡大・縮小率", "toolbox.scale.scaleRatioPlaceholder": "拡大・縮小率を入力。例：2", "toolbox.scale.scaleRatioRequired": "拡大・縮小率を入力してください", "toolbox.scale.scaleType": "拡大・縮小タイプ", "toolbox.scale.scaleTypeOptions.custom": "カスタムサイズ", "toolbox.scale.scaleTypeOptions.fixed": "固定サイズ", "toolbox.scale.scaleTypeOptions.ratio": "比率で拡大・縮小", "toolbox.scale.selectPaperSize": "用紙サイズを選択", "toolbox.scale.stem_append": "_スケーリング", "toolbox.scale.title": "PDF拡大・縮小", "toolbox.scale.width": "幅", "toolbox.scale.widthPlaceholder": "ページの幅を入力 (pt)", "toolbox.scale.widthRequired": "幅を入力してください", "toolbox.split.bookmarkLevel": "目次レベル", "toolbox.split.bookmarkSplit": "目次で分割", "toolbox.split.chunkSize": "チャンクサイズ", "toolbox.split.chunkSizeGreaterThanZero": "チャンクサイズは0より大きい必要があります", "toolbox.split.chunkSizePlaceholder": "チャンクサイズを入力", "toolbox.split.chunkSizeRequired": "チャンクサイズを入力してください。例: 10", "toolbox.split.customSplit": "カスタム分割", "toolbox.split.customSplitSuffix": "カスタム分割", "toolbox.split.description": "PDFファイルを複数のPDFファイルに分割します。均等分割またはカスタム分割をサポートします。", "toolbox.split.desktopOnly": "この機能はデスクトップ版限定", "toolbox.split.enterInteger": "整数を入力してください", "toolbox.split.level1": "レベル1の見出し", "toolbox.split.level2": "レベル2の見出し", "toolbox.split.level3": "レベル3の見出し", "toolbox.split.pleaseSelectFile": "ファイルを選択してください", "toolbox.split.selectBookmarkLevel": "目次レベルを選択", "toolbox.split.splitSuffix": "分割", "toolbox.split.splitType": "分割タイプ", "toolbox.split.title": "PDF分割", "toolbox.split.uniformSplit": "均等分割", "toolbox.split.uniformSplitSuffix": "均等分割", "toolbox.validation.enterFloat": "浮動小数点数を入力してください", "toolbox.validation.enterInteger": "整数を入力してください", "toolbox.validation.enterNonNegativeNumber": "0以上の数値を入力してください", "toolbox.validation.mustBeGreaterThanZero": "0より大きい値である必要があります", "toolbox.watermark.addTab": "透かしを追加", "toolbox.watermark.advancedOptions": "詳細オプション", "toolbox.watermark.createWatermarkImageFailed": "ウォーターマーク画像の作成に失敗", "toolbox.watermark.description": "PDFファイルに透かしを追加または削除します", "toolbox.watermark.desktopOnlyFeature": "この機能はデスクトップ版でのみ利用可能です", "toolbox.watermark.detectWatermarkFailed": "透かしの検出に失敗", "toolbox.watermark.fileNotExist": "入力ファイルが存在しません", "toolbox.watermark.fontFamilyList.sourceHanSansSC": "源ノ角ゴシック", "toolbox.watermark.image.file": "画像ファイル", "toolbox.watermark.image.opacity": "不透明度", "toolbox.watermark.image.position": "位置", "toolbox.watermark.image.rotation": "回転角度", "toolbox.watermark.image.scale": "拡大・縮小率", "toolbox.watermark.imageDataEmpty": "画像データが空です", "toolbox.watermark.invalidWatermarkIndex": "透かしインデックスの形式が無効です", "toolbox.watermark.limit_regin": "領域を限定", "toolbox.watermark.lower_bounds": "閾値下限", "toolbox.watermark.lower_bounds_placeholder": "閾値下限を入力", "toolbox.watermark.lower_bounds_required": "閾値下限を入力してください", "toolbox.watermark.noValidRectangleAnnotation": "指定ページに有効な矩形注釈が見つかりません", "toolbox.watermark.notSupport": "現在サポートされていません", "toolbox.watermark.readImageDataFailed": "画像データの読み取りに失敗", "toolbox.watermark.removeTab": "透かしを削除", "toolbox.watermark.removeWatermarkFailed": "ウォーターマークの削除に失敗", "toolbox.watermark.savePdfFailed": "PDFの保存に失敗", "toolbox.watermark.stepOptions.step1": "ステップ1：透かしインデックスの検出", "toolbox.watermark.stepOptions.step2": "ステップ2：透かしの除去", "toolbox.watermark.step_option": "ステップ", "toolbox.watermark.text.content": "透かしテキスト", "toolbox.watermark.text.customFont": "カスタムフォント", "toolbox.watermark.text.fontColor": "フォントカラー", "toolbox.watermark.text.fontFamily": "フォント", "toolbox.watermark.text.fontFile": "フォントファイル", "toolbox.watermark.text.fontSize": "フォントサイズ", "toolbox.watermark.text.opacity": "不透明度", "toolbox.watermark.text.position": "位置", "toolbox.watermark.text.rotation": "回転角度", "toolbox.watermark.text.selectFontFamily": "フォントを選択", "toolbox.watermark.text.target_color": "ターゲット色", "toolbox.watermark.title": "PDF透かし", "toolbox.watermark.type": "透かしタイプ", "toolbox.watermark.typeOptions.image": "画像", "toolbox.watermark.typeOptions.text": "テキスト", "toolbox.watermark.upper_bounds": "閾値上限", "toolbox.watermark.upper_bounds_invalid": "整数を入力してください", "toolbox.watermark.upper_bounds_placeholder": "閾値上限を入力", "toolbox.watermark.upper_bounds_range": "0～255の整数を入力してください", "toolbox.watermark.upper_bounds_required": "閾値上限を入力してください", "toolbox.watermark.watermarkImageNotExist": "ウォーターマーク画像パスが存在しません", "toolbox.watermark.watermarkImageRequired": "ウォーターマーク画像を選択してください", "toolbox.watermark.watermarkIndexRequired": "ウォーターマークインデックスを入力してください", "toolbox.watermark.watermarkPageRequired": "ウォーターマークのページを指定してください", "toolbox.watermark.watermarkRemoveTypeList.content": "コンテンツ透かし", "toolbox.watermark.watermarkRemoveTypeList.edit_text": "(編集可能)テキスト透かし", "toolbox.watermark.watermarkRemoveTypeList.image": "画像透かし", "toolbox.watermark.watermarkRemoveTypeList.mask": "マスク透かし", "toolbox.watermark.watermarkRemoveTypeList.path": "パス透かし", "toolbox.watermark.watermarkRemoveTypeList.pixel": "ピクセル透かし除去", "toolbox.watermark.watermarkRemoveTypeList.type": "タイプ透かし", "toolbox.watermark.watermarkTextRequired": "ウォーターマークテキストを入力してください", "toolbox.watermark.wm_index": "ウォーターマークインデックス", "toolbox.watermark.wm_index_placeholder": "ウォーターマークインデックスを入力", "toolbox.watermark.wm_page_number": "ウォーターマーク含有ページ", "toolbox.watermark.wm_page_number_invalid": "ページ番号を1つだけ入力してください。例：1", "toolbox.watermark.wm_page_number_placeholder": "ウォーターマーク含有ページ番号を入力。例：1", "toolbox.watermark.wm_page_number_required": "ウォーターマークページ番号を入力してください", "toolbox.watermark.wm_text_cannot_empty": "ウォーターマークテキストは空にできません", "toolbox.watermark.wm_text_to_remove": "ウォーターマークテキスト", "toolbox.watermark.wm_text_to_remove_placeholder": "削除するウォーターマークテキストを入力", "userCenter.annually": "年会員", "userCenter.buyMembership": "メンバーシップを購入", "userCenter.expiryDate": "有効期限", "userCenter.lifetime": "永久会員", "userCenter.memberInfo": "会員情報", "userCenter.memberType": "会員タイプ", "userCenter.monthly": "月会員", "userCenter.noMember": "なし", "userCenter.title": "個人センター", "userCenter.unknown": "不明", "videoNotes.action.delete": "削除", "videoNotes.action.select": "選択", "videoNotes.cloze.freeGuess": "自由推測", "videoNotes.cloze.maskAllGuessAll": "すべて隠してすべて推測", "videoNotes.cloze.maskAllGuessOne": "すべて隠して1つ推測", "videoNotes.cloze.maskOneGuessOne": "1つ隠して1つ推測", "videoNotes.cloze.scratchGuess": "スクラッチ推測", "videoNotes.debug.debugProcessError": "デバッグプロセスエラー", "videoNotes.debug.serverStatusInfo": "サーバーステータス情報", "videoNotes.debug.webSocketDebugComplete": "WebSocketデバッグが完了しました", "videoNotes.debug.webSocketDebugFailed": "WebSocketデバッグに失敗しました", "videoNotes.defaults.deckName": "動画ノート", "videoNotes.dialog.addBilibiliVideo": "Bilibili動画を追加", "videoNotes.dialog.addNetworkVideo": "ネットワーク動画を追加", "videoNotes.dialog.cancel": "キャンセル", "videoNotes.dialog.confirm": "確認", "videoNotes.dialog.selectRootFolder": "ルートフォルダを選択", "videoNotes.dialog.selectStorageLocation": "保存場所を選択", "videoNotes.dialog.selectStorageLocationContent": "ファイルの保存場所を選択してください", "videoNotes.dialog.videoTitle": "動画タイトル", "videoNotes.dialog.videoTitleHint": "動画のタイトルを入力", "videoNotes.dialog.videoUrl": "動画URL", "videoNotes.dialog.videoUrlHint": "動画のURLを入力", "videoNotes.empty.addVideoPrompt": "動画を追加してください", "videoNotes.empty.playlistEmpty": "プレイリストが空です", "videoNotes.error.annotationFailed": "注釈の作成に失敗しました", "videoNotes.error.cannotConnectToBrowserPlugin": "ブラウザプラグインに接続できません", "videoNotes.error.cannotSaveScreenshot": "スクリーンショットを保存できません", "videoNotes.error.cannotVerifyBrowserExtension": "ブラウザ拡張機能を確認できません", "videoNotes.error.checkServerStartup": "サーバーの起動を確認してください", "videoNotes.error.clozeImageFailed": "穴埋め画像の作成に失敗しました", "videoNotes.error.connectionCheckFailed": "接続チェックに失敗しました", "videoNotes.error.customTemplateEmpty": "カスタムテンプレートが空です", "videoNotes.error.deckNameEmpty": "デッキ名が空です", "videoNotes.error.filePermissionRequired": "ファイル権限が必要です", "videoNotes.error.ocrInitFailed": "OCRの初期化に失敗しました", "videoNotes.error.operationFailed": "操作に失敗しました", "videoNotes.error.playPauseCommandFailed": "再生/一時停止コマンドに失敗しました", "videoNotes.error.processVideoError": "動画処理エラー", "videoNotes.error.saveFailed": "保存に失敗しました", "videoNotes.error.screenshotFailed": "スクリーンショットに失敗しました", "videoNotes.error.screenshotRequestFailed": "スクリーンショットリクエストに失敗しました", "videoNotes.error.setRootDirectoryFirst": "まずルートディレクトリを設定してください", "videoNotes.error.textExtractionFailed": "テキスト抽出に失敗しました", "videoNotes.error.timestampRequestFailed": "タイムスタンプリクエストに失敗しました", "videoNotes.error.title": "エラー", "videoNotes.error.webSocketServerNotRunning": "WebSocketサーバーが実行されていません", "videoNotes.file.savePlaylist": "プレイリストを保存", "videoNotes.file.selectPlaylist": "プレイリストを選択", "videoNotes.linkFormat.custom": "カスタム", "videoNotes.menu.openLocal": "ローカル動画を開く", "videoNotes.menu.openNetwork": "ネットワーク動画を開く", "videoNotes.menu.openPlaylist": "プレイリストを開く", "videoNotes.menu.preferences": "設定", "videoNotes.message.annotationCopied": "注釈がコピーされました", "videoNotes.message.settingsSaved": "設定が保存されました", "videoNotes.message.success": "成功", "videoNotes.message.videoAdded": "動画が追加されました", "videoNotes.navigation.playlist": "プレイリスト", "videoNotes.navigation.subtitle": "字幕", "videoNotes.notification.addVideoFirst": "まず動画を追加してください", "videoNotes.notification.annotationFailed": "注釈の作成に失敗しました", "videoNotes.notification.playerSettingsUpdated": "プレイヤー設定が更新されました", "videoNotes.notification.playerSwitchedTo": "プレイヤーが切り替わりました: ", "videoNotes.notification.screenshotCopyFailed": "スクリーンショットのコピーに失敗しました", "videoNotes.notification.screenshotCopySuccess": "スクリーンショットのコピーに成功しました", "videoNotes.notification.timestampCopyFailed": "タイムスタンプのコピーに失敗しました", "videoNotes.notification.timestampCopySuccess": "タイムスタンプのコピーに成功しました", "videoNotes.player.browser": "ブラウザ", "videoNotes.player.builtin": "内蔵", "videoNotes.playlist.empty": "プレイリストが空です", "videoNotes.playlist.loadError": "プレイリストの読み込みエラー", "videoNotes.playlist.loadFailed": "プレイリストの読み込みに失敗しました", "videoNotes.playlist.loadSuccess": "プレイリストの読み込みに成功しました", "videoNotes.playlist.loaded": "プレイリストが読み込まれました", "videoNotes.playlist.saveFailed": "プレイリストの保存に失敗しました", "videoNotes.playlist.saved": "プレイリストが保存されました", "videoNotes.progress.generatingTimestamp": "タイムスタンプを生成中", "videoNotes.progress.gettingScreenshot": "スクリーンショットを取得中", "videoNotes.progress.pleaseWait": "お待ちください", "videoNotes.settings.anki.defaultClozeMode": "デフォルト穴埋めモード", "videoNotes.settings.anki.defaultClozeModeePlaceholder": "デフォルト穴埋めモードを選択", "videoNotes.settings.anki.defaultDeck": "デフォルトデッキ", "videoNotes.settings.anki.defaultDeckPlaceholder": "デフォルトデッキ名を入力", "videoNotes.settings.anki.defaultTags": "デフォルトタグ", "videoNotes.settings.anki.defaultTagsPlaceholder": "デフォルトタグを入力（スペース区切り）", "videoNotes.settings.anki.oneClozePeCard": "1カードにつき1つの穴埋め", "videoNotes.settings.backlink.autoPaste": "自動貼り付け", "videoNotes.settings.backlink.customTemplate": "カスタムテンプレート", "videoNotes.settings.backlink.customTemplatePlaceholder": "カスタムテンプレートを入力", "videoNotes.settings.backlink.enablePathEncoding": "パスエンコーディングを有効にする", "videoNotes.settings.backlink.linkFormat": "リンク形式", "videoNotes.settings.backlink.linkFormatPlaceholder": "リンク形式を選択", "videoNotes.settings.backlink.pauseAfterCopyLink": "リンクコピー後に一時停止", "videoNotes.settings.backlink.pauseAfterScreenshot": "スクリーンショット後に一時停止", "videoNotes.settings.backlink.rootFolder": "ルートフォルダ", "videoNotes.settings.backlink.rootFolderPlaceholder": "ルートフォルダを選択", "videoNotes.settings.backlink.useRelativePath": "相対パスを使用", "videoNotes.settings.category.ankiSettings": "<PERSON><PERSON>設定", "videoNotes.settings.category.backlinkSettings": "バックリンク設定", "videoNotes.settings.category.playbackControl": "再生制御", "videoNotes.settings.category.shortcutSettings": "ショートカット設定", "videoNotes.settings.player.brightnessGesture": "明度ジェスチャー", "videoNotes.settings.player.defaultPlayer": "デフォルトプレイヤー", "videoNotes.settings.player.defaultSpeedValue": "デフォルト再生速度", "videoNotes.settings.player.doubleTapPause": "ダブルタップで一時停止", "videoNotes.settings.player.doubleTapSeek": "ダブルタップでシーク", "videoNotes.settings.player.longPressSpeed": "長押し再生速度", "videoNotes.settings.player.seekSeconds": "シーク秒数", "videoNotes.settings.player.showMiniProgress": "ミニプログレスを表示", "videoNotes.settings.player.volumeGesture": "音量ジェスチャー", "videoNotes.settings.shortcuts.annotScreenshot": "注釈付きスクリーンショット", "videoNotes.settings.shortcuts.backward15": "15秒戻る", "videoNotes.settings.shortcuts.backward5": "5秒戻る", "videoNotes.settings.shortcuts.copyScreenshot": "スクリーンショットをコピー", "videoNotes.settings.shortcuts.copyTimestamp": "タイムスタンプをコピー", "videoNotes.settings.shortcuts.disableAll": "すべて無効", "videoNotes.settings.shortcuts.forward15": "15秒進む", "videoNotes.settings.shortcuts.forward5": "5秒進む", "videoNotes.settings.shortcuts.playPause": "再生/一時停止", "videoNotes.shortcuts.allDisabled": "すべてのショートカットが無効になりました", "videoNotes.shortcuts.allEnabled": "すべてのショートカットが有効になりました", "videoNotes.status.notSet": "未設定", "videoNotes.title.main": "動画ノート", "videoNotes.title.settings": "設定", "videoNotes.tooltip.clearPlaylist": "プレイリストをクリア", "videoNotes.tooltip.openLocal": "ローカル動画を開く", "videoNotes.tooltip.openNetwork": "ネットワーク動画を開く", "videoNotes.tooltip.openPlaylist": "プレイリストを開く", "videoNotes.tooltip.savePlaylist": "プレイリストを保存", "videoNotes.validation.directoryNotExists": "ディレクトリが存在しません", "videoNotes.validation.rootFolderEmpty": "ルートフォルダが空です", "videoNotes.validation.selectDirectory": "ディレクトリを選択してください", "videoNotes.validation.selectDirectoryNotFile": "ファイルではなくディレクトリを選択してください", "watermark.completion.detectionCompleted": "コンテンツウォーターマーク検出完了", "watermark.completion.imageWatermarkDetectionCompleted": "画像ウォーターマーク検出完了", "watermark.completion.pathWatermarkDetectionCompleted": "パスウォーターマーク検出完了", "watermark.completion.pdfToImageCompleted": "ピクセルウォーターマーク検出完了", "watermark.default.watermarkText": "ウォーターマーク", "watermark.error.addImageWatermarkFailed": "画像ウォーターマークの追加に失敗", "watermark.error.addTextWatermarkFailed": "テキストウォーターマークの追加に失敗", "watermark.error.contentWatermarkDetectionFailed": "コンテンツウォーターマーク検出に失敗", "watermark.error.imageWatermarkDetectionFailed": "画像ウォーターマーク検出に失敗", "watermark.filename.watermarkAdded": "_ウォーターマーク追加済み", "watermark.filename.watermarkIndexDetected": "_ウォーターマークインデックス検出", "watermark.filename.watermarkRemoved": "_ウォーターマーク除去版", "watermark.log.font": "フォント", "watermark.log.fontSize": "フォントサイズ", "watermark.log.watermarkText": "ウォーターマークテキスト", "watermark.progress.convertingPdfToImage": "ピクセル分析のためPDFを画像に変換中", "watermark.progress.detectingContentWatermark": "コンテンツウォーターマーク検出中", "watermark.progress.detectingImageWatermark": "画像ウォーターマーク検出中", "watermark.progress.detectingPathWatermark": "パスウォーターマーク検出中", "watermark.progress.processingContentWatermark": "コンテンツウォーターマーク処理中", "watermark.progress.processingEditableTextWatermark": "編集可能テキストウォーターマーク処理中", "watermark.progress.processingImageWatermark": "画像ウォーターマーク処理中", "watermark.progress.processingPathWatermark": "パスウォーターマーク処理中", "watermark.progress.processingPixelWatermark": "ピクセルウォーターマーク処理中"}